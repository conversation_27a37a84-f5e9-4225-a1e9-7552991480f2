package quest

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"

	"github.com/heroiclabs/nakama-common/runtime"
	"google.golang.org/protobuf/encoding/protojson"
)

// InitQuestSystem 初始化任务系统
func InitQuestSystem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule) error {
	// 创建任务数据库表
	err := InitQuest(ctx, logger, db)
	if err != nil {
		logger.Error("Failed to initialize quest tables: %v", err)
		return err
	}

	// 初始化任务缓存
	InitQuestCache()

	// 加载任务数据到缓存
	// err = LoadAllQuestsToCache(ctx, logger, db)
	// if err != nil {
	// 	logger.Error("Failed to load quests to cache: %v", err)
	// 	return err
	// }

	// 注意：训练师任务表需要在trainer包中单独初始化

	// 注意：任务条件配置现在在trainer包中初始化
	// InitQuestConditions() // 已移动到trainer.InitTrainerQuestConditions()

	// 初始化任务奖励配置
	// InitQuestRewards()

	// 启动任务广播调度器
	// broadcastManager := NewQuestBroadcastManager(logger, nk)
	// broadcastManager.StartQuestBroadcastScheduler(ctx, db)

	// 加载任务数据（可选）
	// 注意：如果questInfo.json文件不存在，这个调用会失败但不会影响系统初始化
	err = LoadQuestInfoFromFile(ctx, logger, db)
	if err != nil {
		logger.Warn("Failed to load quest data from file (this is optional): %v", err)
		// 不返回错误，因为这是可选的
	}

	// logger.Info("Quest system initialized successfully")
	return nil
}
func AaaaTest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	InitQuestSystem(ctx, logger, db, nk)
	return "", nil
}

// RegisterQuestRPCs 注册任务相关的RPC函数
// 注意：RPC函数现在在trainer包中，需要在trainer包中注册

// QuestSystemConfig 任务系统配置
type QuestSystemConfig struct {
	EnableBroadcast     bool `json:"enable_broadcast"`       // 是否启用广播
	BroadcastInterval   int  `json:"broadcast_interval"`     // 广播检查间隔（秒）
	MaxQuestsPerTrainer int  `json:"max_quests_per_trainer"` // 每个训练师最大任务数
	QuestTimeoutSeconds int  `json:"quest_timeout_seconds"`  // 任务超时时间（秒）
	EnableQuestLog      bool `json:"enable_quest_log"`       // 是否启用任务日志
	LogRetentionDays    int  `json:"log_retention_days"`     // 日志保留天数
}

// DefaultQuestSystemConfig 默认任务系统配置
var DefaultQuestSystemConfig = QuestSystemConfig{
	EnableBroadcast:     true,
	BroadcastInterval:   60,    // 1分钟
	MaxQuestsPerTrainer: 50,    // 最多50个任务
	QuestTimeoutSeconds: 86400, // 24小时
	EnableQuestLog:      true,
	LogRetentionDays:    30, // 保留30天
}

// LoadQuestSystemConfig 加载任务系统配置
func LoadQuestSystemConfig() QuestSystemConfig {
	// 这里可以从配置文件或环境变量加载配置
	// 目前返回默认配置
	return DefaultQuestSystemConfig
}

// ValidateQuestSystemConfig 验证任务系统配置
func ValidateQuestSystemConfig(config QuestSystemConfig) error {
	if config.BroadcastInterval <= 0 {
		config.BroadcastInterval = 60
	}
	if config.MaxQuestsPerTrainer <= 0 {
		config.MaxQuestsPerTrainer = 50
	}
	if config.QuestTimeoutSeconds <= 0 {
		config.QuestTimeoutSeconds = 86400
	}
	if config.LogRetentionDays <= 0 {
		config.LogRetentionDays = 30
	}
	return nil
}

// CleanupExpiredQuests 清理过期任务
// func CleanupExpiredQuests(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
// 	tx, err := db.Begin()
// 	if err != nil {
// 		return err
// 	}
// 	defer tx.Rollback()

// 	// 清理过期的任务记录
// 	config := LoadQuestSystemConfig()
// 	if config.EnableQuestLog && config.LogRetentionDays > 0 {
// 		retentionTimestamp := time.Now().AddDate(0, 0, -config.LogRetentionDays).Unix()

// 		query := `
// 			DELETE FROM trainer_quest
// 			WHERE quest_status IN (2, 3, 4)
// 			AND update_ts < $1
// 		`

// 		result, err := tx.ExecContext(ctx, query, retentionTimestamp)
// 		if err != nil {
// 			logger.Error("Failed to cleanup expired quests: %v", err)
// 			return err
// 		}

// 		rowsAffected, _ := result.RowsAffected()
// 		logger.Info("Cleaned up %d expired quest records", rowsAffected)
// 	}

// 	return tx.Commit()
// }

// GetQuestSystemStats 获取任务系统统计信息
func GetQuestSystemStats(ctx context.Context, logger runtime.Logger, db *sql.DB) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计总任务数
	var totalQuests int
	err := db.QueryRowContext(ctx, "SELECT COUNT(*) FROM quest").Scan(&totalQuests)
	if err != nil {
		return nil, err
	}
	stats["total_quests"] = totalQuests

	// 统计活跃任务数
	var activeQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM quest WHERE quest_status = 1").Scan(&activeQuests)
	if err != nil {
		return nil, err
	}
	stats["active_quests"] = activeQuests

	// 统计训练师任务总数
	var totalTrainerQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM trainer_quest").Scan(&totalTrainerQuests)
	if err != nil {
		return nil, err
	}
	stats["total_trainer_quests"] = totalTrainerQuests

	// 统计进行中的训练师任务数
	var inProgressQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM trainer_quest WHERE quest_status = 1").Scan(&inProgressQuests)
	if err != nil {
		return nil, err
	}
	stats["in_progress_quests"] = inProgressQuests

	// 统计已完成的训练师任务数
	var completedQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM trainer_quest WHERE quest_status IN (2, 3)").Scan(&completedQuests)
	if err != nil {
		return nil, err
	}
	stats["completed_quests"] = completedQuests

	return stats, nil
}

// HealthCheckQuestSystem 任务系统健康检查
func HealthCheckQuestSystem(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 检查数据库连接
	err := db.PingContext(ctx)
	if err != nil {
		return fmt.Errorf("database connection failed: %v", err)
	}

	// 检查必要的表是否存在
	tables := []string{"quest", "trainer_quest"}
	for _, table := range tables {
		var exists bool
		query := `
			SELECT EXISTS (
				SELECT FROM information_schema.tables 
				WHERE table_schema = 'public' 
				AND table_name = $1
			)
		`
		err := db.QueryRowContext(ctx, query, table).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check table %s: %v", table, err)
		}
		if !exists {
			return fmt.Errorf("table %s does not exist", table)
		}
	}

	logger.Info("Quest system health check passed")
	return nil
}

// LoadQuestInfoFromFile 从questInfo.json文件加载任务数据到数据库
func LoadQuestInfoFromFile(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 从文件读取 JSON 数据
	jsonFile, err := os.ReadFile("/nakama/data/QuestData.json")
	if err != nil {
		log.Printf("Failed to read QuestData.json file: %v", err)
		return fmt.Errorf("failed to read QuestData.json file: %v", err)
	}
	var questData = &MainServer.QuestInfoList{}
	err = protojson.Unmarshal(jsonFile, questData)
	if err != nil {
		log.Printf("Error parsing QuestInfo.json: %v", err)
		return fmt.Errorf("error parsing QuestInfo.json: %v", err)
	}
	if questData == nil {
		log.Printf("QuestData.json is empty")
		return fmt.Errorf("QuestData.json is empty")
	} else {
		addQuestsToCache(ctx, logger, questData.GetQuestInfos())
		return nil
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()
	for _, questInfo := range questData.GetQuestInfos() {
		// 检查任务ID是否有效
		if questInfo.GetQuestId() <= 0 {
			log.Printf("Invalid quest ID: %d", questInfo.GetQuestId())
			continue
		}
		sqlQuestInfo, _ := GetQuestById(ctx, logger, tx, questInfo.GetQuestId())
		if sqlQuestInfo != nil {
			// 如果任务已存在且版本号相同，则跳过
			if sqlQuestInfo.GetVersion() >= questInfo.GetVersion() {
				log.Printf("Quest ID %d already exists with the same version %d, skipping", questInfo.GetQuestId(), questInfo.GetVersion())
				continue
			}
			// 如果任务已存在但版本号不同，则更新任务
			log.Printf("Quest ID %d exists with version %d, updating to version %d", questInfo.GetQuestId(), sqlQuestInfo.GetVersion(), questInfo.GetVersion())
			// 这里可以添加更新逻辑，如果需要的话
		} else {
			// 如果任务不存在，则添加新任务
			log.Printf("Quest ID %d does not exist, adding new quest", questInfo.GetQuestId())
		}
		err = UpsertQuest(ctx, logger, tx, questInfo)
		if err != nil {
			log.Printf("Failed to add/update quest ID %d: %v", questInfo.GetQuestId(), err)
			continue
		}
		log.Printf("Successfully added/updated quest ID %d", questInfo.GetQuestId())

	}

	// processedCount := 0

	// 遍历任务数据并插入数据库
	// for idStr, questInfo := range questInfoMap {
	// 	// 将字符串ID转换为int32
	// 	questId, err := strconv.ParseInt(idStr, 10, 32)
	// 	if err != nil {
	// 		logger.Error("Invalid quest ID format: %s, error: %v", idStr, err)
	// 		continue
	// 	}

	// 	// 设置任务ID并使用AddQuest函数（支持upsert）
	// 	questInfo.QuestId = int32(questId)

	// 	// 使用现有的AddQuest函数，它支持ON CONFLICT DO UPDATE
	// 	err = AddQuest(ctx, logger, tx, questInfo)
	// 	if err != nil {
	// 		logger.Error("Failed to add/update quest ID %d: %v", questId, err)
	// 		continue
	// 	}

	// 	processedCount++
	// 	logger.Debug("Successfully added/updated quest ID %d", questId)
	// }

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	// logger.Info("Quest data loading completed. Processed: %d, Total: %d",
	// 	processedCount, len(questInfoMap))

	return nil
}
