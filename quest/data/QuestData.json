{"quest_infos": [{"quest_id": 10001, "quest_type": 9, "quest_level": 0, "quest_status": 2, "quest_unlock_info": {"quest_unlock_id": 10001, "quest_unlock_conditions": [{"quest_unlock_type": 10, "quest_condition": {"condition_name_id": "", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "single_quest": true, "quest_strict": {"quest_strict_id": 0, "quest_stricts": [{"source": 0, "strict_type": 1, "strict_int_value": "0", "strict_str_value": ""}, {"source": 0, "strict_type": 3, "strict_int_value": "0", "strict_str_value": ""}]}, "quest_reward_info": {"quest_reward_id": 10001, "quest_rewards": [{"quest_reward_type": 7, "quest_reward_rate": 0, "quest_reward_count": 1, "quest_reward_count_rate": 0, "day_whole_netlocked": 0, "quest_reward_value": "", "quest_reward_base_money": 1000}], "random_count": 0}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10002, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": true, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_complete_info": {"quest_complete_id": 10001, "quest_complete_conditions": [{"quest_complete_type": 5, "quest_condition": {"condition_name_id": "", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}], "quest_complete_report_name": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10003, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10004, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10005, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10006, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10007, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10008, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10009, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10010, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10011, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}, {"quest_id": 10012, "quest_type": 0, "quest_level": 0, "quest_status": 2, "single_quest": false, "quest_strict": {"quest_strict_id": 0, "quest_stricts": []}, "quest_start_time": "0", "quest_end_time": "0", "quest_repeat_limit": 0, "quest_repeat_interval": 0, "quest_broadcast": {"quest_broadcast_type": 0, "quest_broadcast_value": ""}, "quest_repeat_limit_time": 0, "version": 0, "quest_cancel_info": {"cancel_interval": 0, "cancel_accept_interval": 0}}], "quest_stricts": [], "quest_unlock_map": {"10001": {"quest_unlock_id": 10001, "quest_unlock_conditions": [{"quest_unlock_type": 10, "quest_condition": {"condition_name_id": "", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10002": {"quest_unlock_id": 10002, "quest_unlock_conditions": [{"quest_unlock_type": 10, "quest_condition": {"condition_name_id": "", "condition_count": 0, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10003": {"quest_unlock_id": 10003, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10004": {"quest_unlock_id": 10004, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10005": {"quest_unlock_id": 10005, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10006": {"quest_unlock_id": 10006, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Galactic", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10007": {"quest_unlock_id": 10007, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Plasma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10008": {"quest_unlock_id": 10008, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Flare", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10009": {"quest_unlock_id": 10009, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Skull", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10010": {"quest_unlock_id": 10010, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Yell", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "10011": {"quest_unlock_id": 10011, "quest_unlock_conditions": [{"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Star", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "11001": {"quest_unlock_id": 11001, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 100, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "11002": {"quest_unlock_id": 11002, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 300, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "11003": {"quest_unlock_id": 11003, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 600, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "11004": {"quest_unlock_id": 11004, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 1000, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "11005": {"quest_unlock_id": 11005, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 1500, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "11006": {"quest_unlock_id": 11006, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 2000, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "11007": {"quest_unlock_id": 11007, "quest_unlock_conditions": [{"quest_unlock_type": 12, "quest_condition": {"condition_name_id": "", "condition_count": 3000, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}]}, "12001": {"quest_unlock_id": 12001, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12002": {"quest_unlock_id": 12002, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 2, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12003": {"quest_unlock_id": 12003, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 3, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12004": {"quest_unlock_id": 12004, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 4, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12005": {"quest_unlock_id": 12005, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 5, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12006": {"quest_unlock_id": 12006, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 6, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12007": {"quest_unlock_id": 12007, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 7, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12008": {"quest_unlock_id": 12008, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 8, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12009": {"quest_unlock_id": 12009, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 9, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "12010": {"quest_unlock_id": 12010, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 10, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13011": {"quest_unlock_id": 13011, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 3, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13012": {"quest_unlock_id": 13012, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 4, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13013": {"quest_unlock_id": 13013, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 5, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13014": {"quest_unlock_id": 13014, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 6, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13015": {"quest_unlock_id": 13015, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 7, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13016": {"quest_unlock_id": 13016, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 8, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13017": {"quest_unlock_id": 13017, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 9, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13018": {"quest_unlock_id": 13018, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 10, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Rocket", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13021": {"quest_unlock_id": 13021, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 3, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13022": {"quest_unlock_id": 13022, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 4, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13023": {"quest_unlock_id": 13023, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 5, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13024": {"quest_unlock_id": 13024, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 6, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13025": {"quest_unlock_id": 13025, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 7, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13026": {"quest_unlock_id": 13026, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 8, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13027": {"quest_unlock_id": 13027, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 9, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13028": {"quest_unlock_id": 13028, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 10, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Magma", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13031": {"quest_unlock_id": 13031, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 3, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13032": {"quest_unlock_id": 13032, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 4, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13033": {"quest_unlock_id": 13033, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 5, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13034": {"quest_unlock_id": 13034, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 6, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13035": {"quest_unlock_id": 13035, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 7, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13036": {"quest_unlock_id": 13036, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 8, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13037": {"quest_unlock_id": 13037, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 9, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}, "13038": {"quest_unlock_id": 13038, "quest_unlock_conditions": [{"quest_unlock_type": 13, "quest_condition": {"condition_name_id": "", "condition_count": 10, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}, {"quest_unlock_type": 11, "quest_condition": {"condition_name_id": "TRAINER_TEAM_Aqua", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}]}}, "quest_complete_map": {"10001": {"quest_complete_id": 10001, "quest_complete_conditions": [{"quest_complete_type": 5, "quest_condition": {"condition_name_id": "", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": false, "is_add": false}}], "quest_complete_report_name": ""}, "10002": {"quest_complete_id": 10002, "quest_complete_conditions": [{"quest_complete_type": 6, "quest_condition": {"condition_name_id": "确认初级道具", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}], "quest_complete_report_name": ""}, "10003": {"quest_complete_id": 10003, "quest_complete_conditions": [{"quest_complete_type": 7, "quest_condition": {"condition_name_id": "确认初级Poke", "condition_count": 0, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}], "quest_complete_report_name": ""}, "10004": {"quest_complete_id": 10004, "quest_complete_conditions": [{"quest_complete_type": 6, "quest_condition": {"condition_name_id": "team_deliver_box", "condition_count": 1, "json_value": "", "time_limit": 0, "is_used": true, "is_add": false}}], "quest_complete_report_name": ""}}, "quest_rewards_map": {"10001": {"quest_reward_id": 10001, "quest_rewards": [{"quest_reward_type": 7, "quest_reward_rate": 0, "quest_reward_count": 1, "quest_reward_count_rate": 0, "day_whole_netlocked": 0, "quest_reward_value": "", "quest_reward_base_money": 1000}], "random_count": 0}}, "quest_type_values": []}