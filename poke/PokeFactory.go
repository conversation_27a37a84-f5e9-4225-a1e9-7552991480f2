package poke

import (
	"context"
	"database/sql"
	"go-nakama-poke/config"

	"go-nakama-poke/proto/MainServer"
	"math/rand"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 初始化随机数种子
func init() {
	rand.Seed(time.Now().UnixMilli())
}

// func AppearPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, rid string, count int) ([]*MainServer.Poke, error) {
// 	// 确保 payload 包含正确的 key
// 	// rid, exists := tool.GetStringFromPayload("r", payload) // 假设有一个函数解析 rid
// 	// if !exists {
// 	// 	return "", runtime.NewError("无效的区域ID", 400) // 错误处理，区域ID不存在
// 	// }

// 	// // 解析 count，默认值为1
// 	// count, exists := tool.GetIntFromPayload("c", payload)
// 	// if !exists {
// 	// 	count = 1
// 	// }

// 	regionId := rid
// 	var results []*MainServer.Poke // 初始化结果 slice

// 	for i := 0; i < count; i++ {
// 		// 调用 randomPokeWithItem 获取结果
// 		result, err := randomPokeWithItem(ctx, logger, tx, nk, regionId)
// 		if err != nil {
// 			return results, runtime.NewError("获取 Pokemon 失败: "+err.Error(), 500)
// 		}

// 		// 根据概率确定是否是闪光 Pokemon
// 		if rand.Float64() < config.ShinyProbability {
// 			result.Shiny = 1 // 标记为闪光，整数值
// 		}

// 		// 将结果添加到 results 中
// 		results = append(results, result)
// 	}

// 	// 序列化结果
// 	// resultStr, err := json.Marshal(results)
// 	// if err != nil {
// 	// 	return [], runtime.NewError("结果序列化失败: "+err.Error(), 500)
// 	// }

//		return results, nil
//	}
func AppearPokes(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, regionKey string, areaKey string, method MainServer.EncounterMethod, count int) ([]*MainServer.Poke, error) {
	var results []*MainServer.Poke // 初始化结果 slice

	for i := 0; i < count; i++ {
		// 调用 randomPokeWithItem 获取结果
		pokemonChance, err := GetPokemonByChance(regionKey, areaKey, method)
		if err != nil {
			return nil, err
		}
		// randomNum := rand.Intn(pokemonChance.MaxChance) + 1
		// level := 100
		// 遍历 Pokémon，逐步减去其 MaxChance
		// for levelStr, chance := range pokemonChance.Level {
		// 	randomNum -= chance
		// 	if randomNum <= 0 {
		// 		level, err = strconv.Atoi(levelStr)
		// 		if err != nil {
		// 			logger.Warn(pokemonChance.Name + ": 产生了错误的等级")
		// 			level = 100
		// 		}
		// 	}
		// }
		poke, err := CreatePoke(ctx, tx, pokemonChance.Name, "", pokemonChance.Level)
		if err != nil {
			return nil, err
		}
		// 根据概率确定是否是闪光 Pokemon
		if rand.Float64() < config.ShinyProbability {
			poke.Shiny = 1 // 标记为闪光，整数值
		}

		// 将结果添加到 results 中
		results = append(results, poke)
	}
	return results, nil
}

// AppearPoke 获取 Pokemon
// func TestRpcAppearPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", runtime.NewError("开始事务失败:"+err.Error(), 500)
// 	}
// 	defer func() {
// 		if err != nil {
// 			tx.Rollback()
// 		} else {
// 			tx.Commit()
// 		}
// 	}()
// 	// 确保 payload 包含正确的 key
// 	rid, exists := tool.GetStringFromPayload("r", payload) // 假设有一个函数解析 rid
// 	if !exists {
// 		return "", runtime.NewError("无效的区域ID", 400) // 错误处理，区域ID不存在
// 	}

// 	// 解析 count，默认值为1
// 	count, exists := tool.GetIntFromPayload("c", payload)
// 	if !exists {
// 		count = 1
// 	}

// 	regionId := rid
// 	var results []*MainServer.Poke // 初始化结果 slice

// 	for i := 0; i < int(count); i++ {
// 		// 调用 randomPokeWithItem 获取结果
// 		result, err := randomPokeWithItem(ctx, logger, tx, nk, regionId)
// 		if err != nil {
// 			return "", runtime.NewError("获取 Pokemon 失败: "+err.Error(), 500)
// 		}

// 		// 根据概率确定是否是闪光 Pokemon
// 		if rand.Float64() < config.ShinyProbability {
// 			result.Shiny = 1 // 标记为闪光，整数值
// 		}

// 		// 将结果添加到 results 中
// 		results = append(results, result)
// 	}

// 	// 序列化结果
// 	resultStr, err := json.Marshal(results)
// 	if err != nil {
// 		return "", runtime.NewError("结果序列化失败: "+err.Error(), 500)
// 	}

// 	return string(resultStr), nil
// }

// 旧的自己配置的poke
// func randomPokeWithItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, nk runtime.NakamaModule, regionId string) (*MainServer.Poke, error) {
// 	// 获取 Region 数据
// 	region, err := GetRegionById(ctx, logger, nk, regionId)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 随机选择一个宝可梦
// 	selectedPoke := randomSelectPoke(region.Pokes)
// 	if selectedPoke == nil {
// 		return nil, runtime.NewError("未找到符合条件的宝可梦", 500)
// 	}

// 	// 随机选择一个物品
// 	selectedItem := randomSelectItem(region, selectedPoke)
// 	selectedItemName := ""
// 	if selectedItem != nil {
// 		selectedItemName = selectedItem.Name
// 	}
// 	return CreatePoke(ctx, tx, selectedPoke.Name, selectedItemName, 5)
// 	// 生成随机 IVs 和 EVs
// 	// ivs := GenerateRandomIVs()
// 	// evs := GenerateRandomEVs()

// 	// 创建并返回最终的宝可梦对象
// 	// finalPoke := &MainServer.Poke{
// 	// 	Name:     selectedPoke.Name,
// 	// 	ItemName: selectedItemName,
// 	// 	Ivs:      ivs,
// 	// 	Evs:      evs,
// 	// 	// Level:  selectedPoke.Level,  // 如果需要，可以根据需求设置Level
// 	// 	// Status: selectedPoke.Status, // 如果有其他字段需要初始化，可以在这里添加
// 	// 	// 根据需求设置其他字段
// 	// }

// 	// return finalPoke, nil
// }

// 如果为空，则随机产生
func GetPoke(name string, item string, level int, ivs *MainServer.PokeStat, evs *MainServer.PokeStat, ability string) (*MainServer.Poke, error) {
	pokeInfo, exists := GetPokemonInfo(name)
	if !exists {
		return nil, runtime.NewError("pokemon not found", 500)
	}
	if ivs == nil {
		ivs = GenerateRandomIVs()
	}
	if evs == nil {
		evs = GenerateRandomEVs()
	}
	if ability == "" {
		ability = pokeInfo.Abilities.Key0
		abilityNum := rand.Int31n(3)
		if abilityNum == 1 && pokeInfo.Abilities.Key1 != "" {
			ability = pokeInfo.Abilities.Key1
		}
	}
	gender := pokeInfo.Gender
	genderRand := rand.Float32() //小于1大于0的小数
	if pokeInfo.GenderRatio != nil && pokeInfo.GenderRatio.M+pokeInfo.GenderRatio.F > 0 {
		if pokeInfo.GenderRatio.M-genderRand > 0 {
			gender = "M"
		} else {
			gender = "F"
		}
	} else {
		gender = "N" // 如果没有性别比例信息，默认设置为无性别
	}
	growthRate, exists := pokemonGrowthRate[pokeInfo.GrowthRate]
	if !exists {
		return nil, runtime.NewError("pokemon growthRate not found", 500)
	}

	experience, exists := growthRate.Levels[int32(level)]
	if !exists {
		return nil, runtime.NewError("pokemon growthRate level not found", 500)
	}
	// Experience
	movefilter := &MoveFilter{
		PokemonName: name,
		Gen:         0,
		Level:       int32(level),
		Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
		IsInit:      true,
	}
	moves := GetFilteredMoves(*movefilter)
	var movesList []*MainServer.PokeSimpleMove
	for i, move := range moves {
		if i >= 4 { // 只取前四个元素
			break
		}
		movesList = append(movesList, &MainServer.PokeSimpleMove{Name: move})
	}
	// 创建并返回最终的宝可梦对象
	finalPoke := &MainServer.Poke{
		Name:       name,
		ItemName:   item,
		Ivs:        ivs,
		Evs:        evs,
		Level:      int32(level),
		Ability:    ability, //随机特性
		Nature:     randomNature(),
		Moves:      movesList,
		Gender:     gender,
		Experience: int64(experience),
		Status:     &MainServer.PokeStatusInfo{}, // 默认状态信息
		BorrowInfo: &MainServer.PokeBorrowInfo{}, // 默认借用信息
		SysExtra: &MainServer.PokeSysExtra{
			Terastal: pokeInfo.Types[0],
		},
		// Level:  selectedPoke.Level,  // 如果需要，可以根据需求设置Level
		// 根据需求设置其他字段
	}
	return finalPoke, nil
}
func CreatePoke(ctx context.Context, tx *sql.Tx, name string, item string, level int) (*MainServer.Poke, error) {
	finalPoke, err := GetPoke(name, item, level, nil, nil, "")
	if err != nil {
		return nil, err
	}
	err = insertPokeData(ctx, tx, finalPoke)
	if err != nil {
		return nil, err
	}
	// gender := pokeInfo.Gender
	// genderRand := rand.Float32() //小于1大于0的小数
	// if pokeInfo.GenderRatio != nil && pokeInfo.GenderRatio.M+pokeInfo.GenderRatio.F > 0 {
	// 	if pokeInfo.GenderRatio.M-genderRand > 0 {
	// 		gender = "M"
	// 	} else {
	// 		gender = "F"
	// 	}
	// } else {
	// 	gender = "N" // 如果没有性别比例信息，默认设置为无性别
	// }
	// // if gender == "" {
	// // 	genderRand := rand.Float32() //小于1大于0的小数
	// // 	if pokeInfo.GenderRatio != nil && pokeInfo.GenderRatio.M+pokeInfo.GenderRatio.F > 0 {
	// // 		if pokeInfo.GenderRatio.M-genderRand > 0 {
	// // 			gender = "M"
	// // 		} else {
	// // 			gender = "F"
	// // 		}
	// // 	}
	// // }
	// growthRate, exists := pokemonGrowthRate[pokeInfo.GrowthRate]
	// if !exists {
	// 	return nil, runtime.NewError("pokemon growthRate not found", 500)
	// }

	// experience, exists := growthRate.Levels[int32(level)]
	// if !exists {
	// 	return nil, runtime.NewError("pokemon growthRate level not found", 500)
	// }
	// // Experience
	// movefilter := &MoveFilter{
	// 	PokemonName: name,
	// 	Gen:         0,
	// 	Level:       int32(level),
	// 	Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
	// 	IsInit:      true,
	// }
	// moves := GetFilteredMoves(*movefilter)
	// var movesList []*MainServer.PokeSimpleMove
	// for i, move := range moves {
	// 	if i >= 4 { // 只取前四个元素
	// 		break
	// 	}
	// 	movesList = append(movesList, &MainServer.PokeSimpleMove{Name: move})
	// }
	// // 创建并返回最终的宝可梦对象
	// finalPoke := &MainServer.Poke{
	// 	Name:       name,
	// 	ItemName:   item,
	// 	Ivs:        ivs,
	// 	Evs:        evs,
	// 	Level:      int32(level),
	// 	Ability:    Ability, //随机特性
	// 	Nature:     randomNature(),
	// 	Moves:      movesList,
	// 	Gender:     gender,
	// 	Experience: int64(experience),
	// 	SysExtra: &MainServer.PokeSysExtra{
	// 		Terastal: pokeInfo.Types[0],
	// 	},
	// 	// Level:  selectedPoke.Level,  // 如果需要，可以根据需求设置Level
	// 	// Status: selectedPoke.Status, // 如果有其他字段需要初始化，可以在这里添加
	// 	// 根据需求设置其他字段
	// }
	insertPokeData(ctx, tx, finalPoke)
	return finalPoke, nil
}

func randomNature() MainServer.Nature {
	natures := []MainServer.Nature{ // 使用枚举类型
		MainServer.Nature_ADAMANT,
		MainServer.Nature_BASHFUL,
		MainServer.Nature_BOLD,
		MainServer.Nature_BRAVE,
		MainServer.Nature_CALM,
		MainServer.Nature_CAREFUL,
		MainServer.Nature_DOCILE,
		MainServer.Nature_GENTLE,
		MainServer.Nature_HARDY,
		MainServer.Nature_HASTY,
		MainServer.Nature_IMPISH,
		MainServer.Nature_JOLLY,
		MainServer.Nature_LAX,
		MainServer.Nature_LONELY,
		MainServer.Nature_MILD,
		MainServer.Nature_MODEST,
		MainServer.Nature_NAIVE,
		MainServer.Nature_NAUGHTY,
		MainServer.Nature_QUIET,
		MainServer.Nature_QUIRKY,
		MainServer.Nature_RASH,
		MainServer.Nature_RELAXED,
		MainServer.Nature_SASSY,
		MainServer.Nature_SERIOUS,
		MainServer.Nature_TIMID,
	}
	// 随机选择一个 Nature
	randomIndex := rand.Intn(len(natures))
	return natures[randomIndex]
}

// 根据概率选择宝可梦
// func randomSelectPoke(pokes []*GameManager.RPoke) *GameManager.RPoke {
// 	totalProb := 0.0
// 	for _, poke := range pokes {
// 		totalProb += float64(poke.P)
// 	}

// 	randomPoint := rand.Float64() * totalProb
// 	currentProb := 0.0

// 	for _, poke := range pokes {
// 		currentProb += float64(poke.P)
// 		if randomPoint <= currentProb {
// 			return poke
// 		}
// 	}
// 	return nil
// }

// // 根据概率选择物品
// func randomSelectItem(region *GameManager.Region, poke *GameManager.RPoke) *GameManager.RPokeItem {
// 	// 先从 Region 的 items 选择
// 	item := randomSelectFromItems(region.Items)
// 	if item != nil {
// 		return item
// 	}

// 	// 如果 Region 没有物品，尝试从 Poke 的 items 选择
// 	return randomSelectFromItems(poke.Items)
// }

// func randomSelectFromItems(items []*GameManager.RPokeItem) *GameManager.RPokeItem {
// 	if len(items) == 0 {
// 		return nil // 如果没有物品，直接返回 nil
// 	}

// 	// 计算所有物品的总概率值
// 	totalProb := 0.0
// 	for _, item := range items {
// 		totalProb += float64(item.P)
// 	}

// 	// 如果总概率为 0，直接返回 nil
// 	if totalProb == 0 {
// 		return nil
// 	}

// 	// 生成一个随机数，范围是 [0, totalProb)
// 	randomPoint := rand.Float64() * totalProb

// 	// 遍历物品并累加概率，找到随机数落在的物品
// 	currentProb := 0.0
// 	var selectedItem *GameManager.RPokeItem
// 	for _, item := range items {
// 		currentProb += float64(item.P)
// 		if randomPoint <= currentProb {
// 			selectedItem = item
// 			break
// 		}
// 	}

// 	// 如果没有选中任何物品，返回 nil
// 	if selectedItem == nil {
// 		return nil
// 	}

// 	// 生成一个新的随机数 [0, 1)，并与选中物品的 P 值进行比较
// 	secondRandom := rand.Float64()
// 	if secondRandom <= float64(selectedItem.P) {
// 		return selectedItem // 随机数小于物品的 P 值，返回该物品
// 	}

// 	// 第二次随机失败，返回 nil
// 	return nil
// }

// 随机生成IVs，每项最大值为31
// 根据不同等级产生随机IVs
func GenerateRandomIVs() *MainServer.PokeStat {
	// 定义各个等级的概率
	vProbabilities := map[int]float64{
		6: 0.005,
		5: 0.05,
		4: 0.5,
		3: 3,
		2: 7,
		1: 15,
		0: 100, // normal 的概率是剩下的
	}

	// 确定要生成的等级：normal, 1v, 2v, 3v, ...
	vLevel := determineVLevel(vProbabilities)

	// 创建一个初始IVs全为随机值（0-31）
	ivs := &MainServer.PokeStat{
		Hp:  rand.Int31n(32),
		Atk: rand.Int31n(32),
		Def: rand.Int31n(32),
		Spa: rand.Int31n(32),
		Spd: rand.Int31n(32),
		Spe: rand.Int31n(32),
	}

	// 如果 vLevel 大于0，则从6项属性中随机选出vLevel个，将其值设置为31
	if vLevel > 0 {
		// 获取属性名称的slice
		ivAttributes := []string{"Hp", "Atk", "Def", "Spa", "Spd", "Spe"}

		// 随机打乱属性顺序
		rand.Shuffle(len(ivAttributes), func(i, j int) { ivAttributes[i], ivAttributes[j] = ivAttributes[j], ivAttributes[i] })

		// 为前vLevel个属性设置满值31
		for i := 0; i < vLevel; i++ {
			switch ivAttributes[i] {
			case "Hp":
				ivs.Hp = 31
			case "Atk":
				ivs.Atk = 31
			case "Def":
				ivs.Def = 31
			case "Spa":
				ivs.Spa = 31
			case "Spd":
				ivs.Spd = 31
			case "Spe":
				ivs.Spe = 31
			}
		}
	}

	return ivs
}

// 辅助函数：根据给定的概率分布选择 vLevel (0 表示 normal)
func determineVLevel(probabilities map[int]float64) int {
	// 计算总权重
	var totalWeight float64
	for _, weight := range probabilities {
		totalWeight += weight
	}

	// 生成一个0到总权重之间的随机数
	randValue := rand.Float64() * totalWeight

	// 根据概率分布选择vLevel
	for vLevel, probability := range probabilities {
		if randValue < probability {
			return vLevel
		}
		randValue -= probability
	}

	// 默认返回 normal (0)
	return 0
}

// 随机生成EVs，总和不能超过510，单项最大值为252
func GenerateRandomEVs() *MainServer.PokeStat {
	totalEVs := int32(510)
	evMax := int32(252)
	evs := []int32{0, 0, 0, 0, 0, 0} // 用于存放随机生成的六项EV

	// 随机分配510点EVs
	for totalEVs > 0 {
		for i := 0; i < len(evs); i++ {
			if totalEVs == 0 {
				break
			}
			// 为每个属性分配随机数量的EV
			if evs[i] < evMax {
				evIncrement := rand.Int31n(int32(min(evMax-evs[i], totalEVs)) + 1) // 分配随机的EV值
				evs[i] += evIncrement
				totalEVs -= int32(evIncrement)
			}
		}
	}

	return &MainServer.PokeStat{
		Hp:  evs[0],
		Atk: evs[1],
		Def: evs[2],
		Spa: evs[3],
		Spd: evs[4],
		Spe: evs[5],
	}
}

// 辅助函数，用于获取较小的数值
func min(a, b int32) int32 {
	if a < b {
		return a
	}
	return b
}

// type BreedingItems struct {
// 	RedString     bool                 // 红线
// 	ShinyCharm    bool                 // 闪耀护符
// 	ShinyDrug     bool                 // 闪光药物
// 	ImmortalCharm bool                 // 不死护符
// 	StatItems     *MainServer.PokeStat // 能力遗传道具
// }

func BreedPokemon(ctx context.Context, tx *sql.Tx, father, mother *MainServer.Poke,
	fatherItems, motherItems *MainServer.BreedingItems) (*MainServer.Poke, bool, bool, error) {

	// 决定种族
	babyName := determineSpecies(father, mother)

	// 继承技能
	moves := inheritMoves(father, mother)

	// 继承能力值
	ivs := inheritIVs(father, mother, fatherItems, motherItems)

	// 决定特性
	ability := inheritAbility(father, mother)

	// 决定是否为闪光
	shiny := determineShiny(father, mother, fatherItems, motherItems)

	// 决定精灵球
	ballName := inheritBall(father, mother)

	// 决定性格
	nature := inheritNature(father, mother)

	terastal := inheritTerastal(father, mother)

	// 计算死亡概率
	fatherDied, motherDied := calculateDeathProbability(father, mother, ivs,
		fatherItems, motherItems)

	// 创建新的宝可梦
	baby := &MainServer.Poke{
		Name:     babyName,
		Moves:    moves,
		Ivs:      ivs,
		Ability:  ability,
		Shiny:    shiny,
		BallName: ballName,
		Nature:   nature,
		Level:    1,
		Evs:      &MainServer.PokeStat{},    // 新生宝可梦EVs为0
		Gender:   determineGender(babyName), // 根据种族决定性别
		Egg:      true,
		SysExtra: &MainServer.PokeSysExtra{
			Terastal: terastal,
		},
	}

	// 保存到数据库
	err := insertPokeData(ctx, tx, baby)
	if err != nil {
		return nil, false, false, err
	}

	return baby, fatherDied, motherDied, nil
}

func CanBreed(poke1, poke2 *MainServer.Poke) (*MainServer.Poke, *MainServer.Poke, bool) {
	// 检查是否有不可孵化蛋的蛋组
	poke1Data, _ := GetPokemonInfo(poke1.Name)
	poke2Data, _ := GetPokemonInfo(poke2.Name)

	// 检查是否有不可孵化的蛋组
	for _, group := range poke1Data.EggGroups {
		if group == "Undiscovered" {
			return poke1, poke2, false
		}
	}
	for _, group := range poke2Data.EggGroups {
		if group == "Undiscovered" {
			return poke1, poke2, false
		}
	}

	// 如果其中一个是百变怪，直接返回true，不需要检查性别
	if isDitto(poke1) || isDitto(poke2) {
		// 如果poke1是百变怪，返回poke2作为母方
		if isDitto(poke1) {
			return poke2, poke1, true
		}
		// 如果poke2是百变怪，返回poke1作为母方
		return poke1, poke2, true
	}

	// 检查性别
	if poke1.Gender == poke2.Gender {
		return poke1, poke2, false // 同性无法繁殖
	}

	// 确定父方和母方
	var father, mother *MainServer.Poke
	if poke1.Gender == "M" {
		father, mother = poke1, poke2
	} else {
		father, mother = poke2, poke1
	}

	// 检查蛋组是否匹配
	hasMatchingEggGroup := false
	for _, fatherGroup := range poke1Data.EggGroups {
		for _, motherGroup := range poke2Data.EggGroups {
			if fatherGroup == motherGroup {
				hasMatchingEggGroup = true
				break
			}
		}
		if hasMatchingEggGroup {
			break
		}
	}

	if !hasMatchingEggGroup {
		return father, mother, false
	}

	return father, mother, true
}

func inheritTerastal(father, mother *MainServer.Poke) string {
	//随机father或者mother的terastal
	if rand.Float64() < 0.5 {
		return father.SysExtra.Terastal
	}
	return mother.SysExtra.Terastal
}
func inheritMoves(father, mother *MainServer.Poke) []*MainServer.PokeSimpleMove {
	var moves []*MainServer.PokeSimpleMove
	// 随机选择父母各一个技能
	if len(father.Moves) > 0 {
		moves = append(moves, father.Moves[rand.Intn(len(father.Moves))])
	}
	if len(mother.Moves) > 0 {
		moves = append(moves, mother.Moves[rand.Intn(len(mother.Moves))])
	}
	return moves
}

func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func inheritIVs(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) *MainServer.PokeStat {
	ivs := &MainServer.PokeStat{}
	usedStats := &MainServer.PokeStat{}
	inheritCount := 2

	if fatherItems.RedString || motherItems.RedString {
		inheritCount = 4
	}

	// 统一处理父母方的遗传道具
	type parentInfo struct {
		poke  *MainServer.Poke
		items *MainServer.BreedingItems
	}
	parents := []parentInfo{
		{father, fatherItems},
		{mother, motherItems},
	}

	stats := []struct {
		get   func(*MainServer.PokeStat) int32
		set   func(*MainServer.PokeStat, int32)
		getIV func(*MainServer.Poke) int32
	}{
		{func(ps *MainServer.PokeStat) int32 { return ps.Hp }, func(ps *MainServer.PokeStat, v int32) { ps.Hp = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Hp }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Atk }, func(ps *MainServer.PokeStat, v int32) { ps.Atk = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Atk }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Def }, func(ps *MainServer.PokeStat, v int32) { ps.Def = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Def }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spa }, func(ps *MainServer.PokeStat, v int32) { ps.Spa = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spa }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spd }, func(ps *MainServer.PokeStat, v int32) { ps.Spd = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spd }},
		{func(ps *MainServer.PokeStat) int32 { return ps.Spe }, func(ps *MainServer.PokeStat, v int32) { ps.Spe = v }, func(p *MainServer.Poke) int32 { return p.Ivs.Spe }},
	}

	// 先处理固定遗传
	for _, s := range stats {
		for _, p := range parents {
			if p.items.StatItems != nil && s.get(p.items.StatItems) > 0 && s.get(usedStats) == 0 {
				s.set(usedStats, 1)
				s.set(ivs, s.getIV(p.poke))
				break
			}
		}
	}

	// 统计已使用数
	usedCount := int(usedStats.Hp + usedStats.Atk + usedStats.Def + usedStats.Spa + usedStats.Spd + usedStats.Spe)
	remainingInherit := minInt(inheritCount-usedCount, 6-usedCount)

	// 随机遗传
	for i := 0; i < remainingInherit; i++ {
		available := []int{}
		for idx, s := range stats {
			if s.get(usedStats) == 0 {
				available = append(available, idx)
			}
		}
		if len(available) == 0 {
			break
		}
		choice := available[rand.Intn(len(available))]
		fromFather := rand.Float64() < 0.5
		if fromFather {
			stats[choice].set(ivs, stats[choice].getIV(father))
		} else {
			stats[choice].set(ivs, stats[choice].getIV(mother))
		}
		stats[choice].set(usedStats, 1)
	}

	// 剩下的未遗传项，随机赋值
	for _, s := range stats {
		if s.get(usedStats) == 0 {
			s.set(ivs, rand.Int31n(32))
		}
	}

	return ivs
}

func inheritAbility(father, mother *MainServer.Poke) string {
	// 处理百变怪情况
	if isDitto(father) || isDitto(mother) {
		return getNormalAbility(father, mother)
	}

	// 计算隐藏特性概率
	hiddenAbilityProb := 0.0

	// 获取父母宝可梦的数据
	fatherData, _ := GetPokemonInfo(father.Name)
	motherData, _ := GetPokemonInfo(mother.Name)

	if isHiddenAbility(father.Ability, fatherData) && isHiddenAbility(mother.Ability, motherData) {
		hiddenAbilityProb = 0.6
	} else if isHiddenAbility(father.Ability, fatherData) || isHiddenAbility(mother.Ability, motherData) {
		hiddenAbilityProb = 0.3
	}

	if rand.Float64() < hiddenAbilityProb {
		return getHiddenAbility(father, mother)
	}
	return getNormalAbility(father, mother)
}

func determineShiny(father, mother *MainServer.Poke, fatherItems, motherItems *MainServer.BreedingItems) int32 {
	shinyProb := 1.0 / 10000.0 // 基础概率

	if father.Shiny == 1 && mother.Shiny == 1 {
		shinyProb = 1.0 / 6000.0
	} else if father.Shiny == 1 || mother.Shiny == 1 {
		shinyProb = 1.0 / 8000.0
	}

	if fatherItems.ShinyCharm || motherItems.ShinyCharm {
		shinyProb = 1.0 / 4000.0
	}

	if fatherItems.ShinyDrug || motherItems.ShinyDrug {
		shinyProb = 1.0 / 1000.0
	}

	if rand.Float64() < shinyProb {
		return 1
	}
	return 0
}

func inheritBall(father, mother *MainServer.Poke) string {
	if rand.Float32() < 0.5 {
		return father.BallName
	}
	return mother.BallName
}

func inheritNature(father, mother *MainServer.Poke) MainServer.Nature {
	rand := rand.Float32()
	if rand < 0.4 {
		return father.Nature
	} else if rand < 0.8 {
		return mother.Nature
	}
	return randomNature()
}

func calculateDeathProbability(father, mother *MainServer.Poke,
	babyIvs *MainServer.PokeStat, fatherItems, motherItems *MainServer.BreedingItems) (bool, bool) {

	// 如果有不死护符，则不会死亡
	if fatherItems.ImmortalCharm {
		return false, calculateSingleDeathProb(mother, mother.BreedCount, babyIvs)
	}
	if motherItems.ImmortalCharm {
		return calculateSingleDeathProb(father, father.BreedCount, babyIvs), false
	}

	// 检查是否有5项31
	if countMaxIVs(babyIvs) >= 5 {
		return true, true
	}

	// 根据繁殖次数计算死亡概率
	motherDeathProb := 0.35
	switch mother.BreedCount {
	case 2:
		motherDeathProb = 0.6
	case 3:
		motherDeathProb = 0.8
	default:
		if mother.BreedCount >= 4 {
			motherDeathProb = 0.95
		}
	}
	fatherDeathProb := 0.35
	switch father.BreedCount {
	case 2:
		fatherDeathProb = 0.6
	case 3:
		fatherDeathProb = 0.8
	default:
		if father.BreedCount >= 4 {
			fatherDeathProb = 0.95
		}
	}

	return rand.Float64() < fatherDeathProb, rand.Float64() < motherDeathProb
}

// 辅助函数
func setValue(stat *MainServer.PokeStat, name string, value int32) {
	switch name {
	case "Hp":
		stat.Hp = value
	case "Atk":
		stat.Atk = value
	case "Def":
		stat.Def = value
	case "Spa":
		stat.Spa = value
	case "Spd":
		stat.Spd = value
	case "Spe":
		stat.Spe = value
	}
}

func getValue(stat *MainServer.PokeStat, name string) int32 {
	switch name {
	case "Hp":
		return stat.Hp
	case "Atk":
		return stat.Atk
	case "Def":
		return stat.Def
	case "Spa":
		return stat.Spa
	case "Spd":
		return stat.Spd
	case "Spe":
		return stat.Spe
	}
	return 0
}

func countMaxIVs(ivs *MainServer.PokeStat) int {
	count := 0
	if ivs.Hp == 31 {
		count++
	}
	if ivs.Atk == 31 {
		count++
	} else if ivs.Atk == 0 { //攻击为0
		count++
	}
	if ivs.Def == 31 {
		count++
	}
	if ivs.Spa == 31 {
		count++
	}
	if ivs.Spd == 31 {
		count++
	}
	if ivs.Spe == 31 {
		count++
	} else if ivs.Spe == 0 { //速度为0
		count++
	}
	return count
}

func calculateSingleDeathProb(poke *MainServer.Poke, breedCount int32, babyIvs *MainServer.PokeStat) bool {
	if countMaxIVs(babyIvs) >= 5 {
		return true
	}

	deathProb := 0.35
	switch breedCount {
	case 2:
		deathProb = 0.6
	case 3:
		deathProb = 0.8
	default:
		if breedCount >= 4 {
			deathProb = 0.95
		}
	}

	return rand.Float64() < deathProb
}

func isDitto(poke *MainServer.Poke) bool {
	return poke.Name == "Ditto"
}

func determineSpecies(father, mother *MainServer.Poke) string {
	if isDitto(father) {
		return mother.Name
	}
	if isDitto(mother) {
		return father.Name
	}
	return mother.Name
}

func determineGender(species string) string {
	pokemonData, exists := GetPokemonInfo(species)
	if !exists {
		return "M" // 默认返回公性
	}

	// 检查是否有固定性别
	if pokemonData.Gender != "" {
		return pokemonData.Gender
	}

	// 检查是否有性别比例
	if pokemonData.GenderRatio == nil ||
		(pokemonData.GenderRatio.M == 0 && pokemonData.GenderRatio.F == 0) || (pokemonData.GenderRatio.M == -1 && pokemonData.GenderRatio.F == -1) {
		return "" // 无性别
	}

	// 根据性别比例随机决定
	randNum := rand.Float32()
	if randNum < pokemonData.GenderRatio.F {
		return "F"
	}
	return "M"
}

// 判断是否为隐藏特性
func isHiddenAbility(ability string, pokemonData *MainServer.PSPokemonData) bool {
	if pokemonData == nil {
		return false
	}
	return ability == pokemonData.Abilities.H
}

// 获取普通特性
func getNormalAbility(father, mother *MainServer.Poke) string {
	// 获取非百变怪的宝可梦数据
	var pokemonData *MainServer.PSPokemonData
	if !isDitto(mother) {
		pokemonData, _ = GetPokemonInfo(mother.Name)
	} else {
		pokemonData, _ = GetPokemonInfo(father.Name)
	}

	// 如果数据不存在，返回默认特性
	if pokemonData == nil {
		return "Overgrow" // 默认特性
	}

	// 80%概率继承key0特性，30%概率继承key1特性（如果存在）
	if pokemonData.Abilities.Key1 != "" && rand.Float32() < 0.3 {
		return pokemonData.Abilities.Key1
	}
	return pokemonData.Abilities.Key0
}

// 获取隐藏特性
func getHiddenAbility(father, mother *MainServer.Poke) string {
	// 获取非百变怪的宝可梦数据
	var pokemonData *MainServer.PSPokemonData
	if !isDitto(mother) {
		pokemonData, _ = GetPokemonInfo(mother.Name)
	} else {
		pokemonData, _ = GetPokemonInfo(father.Name)
	}

	if pokemonData == nil || pokemonData.Abilities.H == "" {
		return getNormalAbility(father, mother) // 如果没有隐藏特性，返回普通特性
	}
	return pokemonData.Abilities.H
}
