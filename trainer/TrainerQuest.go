package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/quest"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 表名常量
const TableTrainerQuest = "trainer_quest"

// InitTrainerQuestTable 初始化训练师任务表
func initTrainerQuestTable(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id SERIAL PRIMARY KEY,
			tid BIGINT NOT NULL,
			quest_id BIGINT NOT NULL,
			quest_type INTEGER NOT NULL DEFAULT 0,
			quest_status INTEGER NOT NULL DEFAULT 0,
			quest_current_info JSONB,
			quest_start_time BIGINT NOT NULL DEFAULT 0,
			quest_end_time BIGINT NOT NULL DEFAULT 0,
			quest_repeat_limit_time INTEGER NOT NULL DEFAULT 0,
			quest_info JSONB,
			create_ts BIGINT NOT NULL DEFAULT 0,
			update_ts BIGINT NOT NULL DEFAULT 0
		)
	`, TableTrainerQuest)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("Failed to create trainer_quest table: %v", err)
		return err
	}

	// 创建索引
	indexQueries := []string{
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_tid ON %s (tid)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_status ON %s (quest_status)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_type ON %s (quest_type)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_tid_status ON %s (tid, quest_status)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_parent ON %s (((quest_current_info->>'parent_quest_id')::int))", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_tid_parent ON %s (tid, ((quest_current_info->>'parent_quest_id')::int))", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_quest_id ON %s (quest_id)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_tid_quest_id ON %s (tid, quest_id)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_update_ts ON %s (update_ts)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_id_parent ON %s (id, ((quest_current_info->>'parent_quest_id')::int))", TableTrainerQuest),
	}

	for _, indexQuery := range indexQueries {
		_, err := db.ExecContext(ctx, indexQuery)
		if err != nil {
			logger.Error("Failed to create index: %v", err)
			return err
		}
	}

	// 初始化任务条件配置
	InitTrainerQuestConditions()

	logger.Info("Trainer quest table and conditions initialized successfully")
	return nil
}

// initializeQuestCurrentInfo 初始化任务当前信息结构
func initializeQuestCurrentInfo(questInfo *MainServer.QuestInfo, parentQuestId int32) *MainServer.TrainerQuestCurrentInfo {
	if questInfo == nil {
		return &MainServer.TrainerQuestCurrentInfo{
			QuestIdList:           []int32{},
			ParentQuestId:         parentQuestId,
			QuestDefaultCondition: make(map[string]int32),
			QuestProgress:         make(map[string]int32),
			CompleteQuestMap:      make(map[int32]bool),
		}
	}

	// 初始化默认条件
	defaultConditions := extractDefaultConditions(questInfo)

	return &MainServer.TrainerQuestCurrentInfo{
		QuestIdList:           []int32{}, // 初始为空，后续会填充子任务ID
		ParentQuestId:         parentQuestId,
		QuestDefaultCondition: defaultConditions,
		QuestProgress:         make(map[string]int32),
		CompleteQuestMap:      make(map[int32]bool),
	}
}

// extractDefaultConditions 从任务信息中提取默认完成条件
func extractDefaultConditions(questInfo *MainServer.QuestInfo) map[string]int32 {
	defaultConditions := make(map[string]int32)

	if questInfo == nil || questInfo.QuestCompleteInfo == nil {
		return defaultConditions
	}

	// 从完成条件中提取默认值
	for _, condition := range questInfo.QuestCompleteInfo.QuestCompleteConditions {
		if condition.QuestCondition != nil {
			if condition.QuestCompleteType == MainServer.QuestCompleteType_QuestCompleteType_battle_poke && condition.QuestCondition.ConditionNameId != "" {
				// key := fmt.Sprintf("%s_%s", condition.QuestCompleteType.String(), condition.QuestCondition.ConditionNameId)
				defaultConditions[condition.QuestCondition.ConditionNameId] = condition.QuestCondition.ConditionCount
			}
		}
	}

	return defaultConditions
}

// createSubQuests 根据任务类型创建子任务
func createSubQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, mainQuest *MainServer.TrainerQuest, questInfo *MainServer.QuestInfo) error {
	var subQuestIds []int32

	if questInfo.LinearQuests != nil && len(questInfo.LinearQuests.QuestIds) > 0 {
		// 线性任务：只创建第一个子任务
		firstQuestId := questInfo.LinearQuests.QuestIds[0]
		subQuestId, err := createSingleSubQuest(ctx, logger, tx, mainQuest, firstQuestId)
		if err != nil {
			return fmt.Errorf("failed to create linear sub quest: %v", err)
		}
		subQuestIds = append(subQuestIds, subQuestId)

	} else if questInfo.CurrentQuests != nil && len(questInfo.CurrentQuests.QuestIds) > 0 {
		// 并行任务：创建所有子任务
		for _, questId := range questInfo.CurrentQuests.QuestIds {
			subQuestId, err := createSingleSubQuest(ctx, logger, tx, mainQuest, questId)
			if err != nil {
				return fmt.Errorf("failed to create parallel sub quest %d: %v", questId, err)
			}
			subQuestIds = append(subQuestIds, subQuestId)
		}
	} else {
		// 单一任务：不需要创建子任务，主任务本身就是执行任务
		logger.Info("Single quest, no sub quests needed for quest %d", questInfo.QuestId)
		return nil
	}

	// 更新主任务的quest_id_list
	mainQuest.QuestCurrentInfo.QuestIdList = subQuestIds
	_, err := UpsertTrainerQuest(ctx, logger, tx, mainQuest)
	if err != nil {
		return fmt.Errorf("failed to update main quest with sub quest ids: %v", err)
	}

	logger.Info("Created %d sub quests for main quest %d", len(subQuestIds), mainQuest.Id)
	return nil
}

// createSingleSubQuest 创建单个子任务
func createSingleSubQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, mainQuest *MainServer.TrainerQuest, subQuestId int32) (int32, error) {
	// 从缓存或数据库获取子任务信息
	subQuestInfo, err := quest.GetAndSimpleCheckQuestById(ctx, logger, tx, subQuestId)
	if err != nil {
		return 0, fmt.Errorf("failed to get sub quest info %d: %v", subQuestId, err)
	}

	// 创建子任务记录
	subQuest := &MainServer.TrainerQuest{
		Id:                   0, // 自动生成
		Tid:                  mainQuest.Tid,
		QuestId:              int64(subQuestId),
		QuestType:            subQuestInfo.QuestType,
		QuestStatus:          MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
		QuestCurrentInfo:     initializeQuestCurrentInfo(subQuestInfo, int32(mainQuest.Id)), // 设置父任务ID
		QuestStartTime:       mainQuest.QuestStartTime,
		QuestEndTime:         0,
		QuestRepeatLimitTime: subQuestInfo.QuestRepeatLimitTime,
		QuestInfo:            subQuestInfo,
		UpdateTs:             time.Now().Unix(),
	}

	// 插入子任务到数据库
	subQuestDbId, err := UpsertTrainerQuest(ctx, logger, tx, subQuest)
	if err != nil {
		return 0, fmt.Errorf("failed to insert sub quest: %v", err)
	}

	logger.Info("Created sub quest %d (db_id: %d) for main quest %d", subQuestId, subQuestDbId, mainQuest.Id)
	return int32(subQuestDbId), nil
}

// AcceptQuest 接受任务
func AcceptQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questId int32) (*MainServer.TrainerQuest, error) {
	questInfo, err := quest.GetAndSimpleCheckQuestById(ctx, logger, tx, questId)
	if err != nil {
		return nil, fmt.Errorf("quest not found: %v", err)
	}
	if questInfo.QuestType == MainServer.QuestType_QuestType_once {
		questInfo, err := GetTrainerQuest(ctx, logger, tx, trainer.Id, questId)
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if questInfo != nil {
			return nil, fmt.Errorf("quest already accepted")
		}
	} else {
		sqlQuestInfo, _ := GetTrainerQuest(ctx, logger, tx, trainer.Id, questId)
		if sqlQuestInfo != nil {
			change, err := tryUpdateQuestEnd(ctx, logger, tx, sqlQuestInfo)
			if err != nil {
				return nil, fmt.Errorf("failed to get trainer quest: %v", err)
			}
			if !change {
				return nil, fmt.Errorf("quest already accepted")
			}
		}
	}
	if questInfo.QuestRepeatLimit > 0 {
		// GetTrainerQuestList
		startTs := int64(0)
		interval := int64(0)
		if questInfo.QuestType == MainServer.QuestType_QuestType_daily {
			//今天0点0分0秒
			startTs = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local).Unix()
			interval = 24 * 60 * 60 // 24小时
			// if sqlQuest != nil && sqlQuest.QuestStartTime >= todayStartTs {
			// 	return nil, fmt.Errorf("daily quest already accepted today")
			// }
		} else if questInfo.QuestType == MainServer.QuestType_QuestType_weekly {
			// 本周的开始时间（假设周一为一周的开始）
			startTs = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()-int(time.Now().Weekday())+1, 0, 0, 0, 0, time.Local).Unix()
			interval = 7 * 24 * 60 * 60 // 7天
			// if sqlQuest != nil && sqlQuest.QuestStartTime >= weekStartTs {
			// 	return nil, fmt.Errorf("weekly quest already accepted this week")
			// }
		}
		sqlQuests, err := GetTrainerQuestsByTime(ctx, logger, tx, trainer.Id, questId, startTs, interval)
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if len(sqlQuests) >= int(questInfo.QuestRepeatLimit) {
			return nil, fmt.Errorf("quest repeat limit reached: %d", questInfo.QuestRepeatLimit)
		}
	}
	if questInfo.QuestRepeatInterval > 0 {
		sqlQuests, err := GetTrainerQuestsByTime(ctx, logger, tx, trainer.Id, questId, time.Now().Unix(), int64(questInfo.QuestRepeatInterval))
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if len(sqlQuests) > 0 {
			return nil, fmt.Errorf("quest repeat interval not met, please wait %d seconds", questInfo.QuestRepeatInterval)
		}
	}
	checked, err := CheckQuestUnlockConditions(ctx, logger, tx, trainer, questInfo.QuestUnlockInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to check quest unlock conditions: %v", err)
	}
	if !checked {
		return nil, fmt.Errorf("quest unlock conditions not met")
	}
	// 暂时跳过任务验证
	// var questInfo *MainServer.QuestInfo

	// 检查任务时间和解锁条件
	nowTs := time.Now().Unix()
	if questInfo != nil {
		if questInfo.QuestStartTime > 0 && nowTs < questInfo.QuestStartTime {
			return nil, fmt.Errorf("quest not started yet")
		}
		if questInfo.QuestEndTime > 0 && nowTs > questInfo.QuestEndTime {
			return nil, fmt.Errorf("quest has ended")
		}

		// 检查解锁条件
		unlocked, err := CheckQuestUnlockConditions(ctx, logger, tx, trainer, questInfo.QuestUnlockInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to check unlock conditions: %v", err)
		}
		if !unlocked {
			return nil, fmt.Errorf("quest unlock conditions not met")
		}
	}

	// 允许重复接受任务，不再检查是否已经接受过

	// 创建训练师任务记录
	trainerQuest := &MainServer.TrainerQuest{
		Id:                   0, // 自动生成
		Tid:                  trainer.Id,
		QuestId:              int64(questId),
		QuestType:            questInfo.QuestType,
		QuestStatus:          MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
		QuestCurrentInfo:     initializeQuestCurrentInfo(questInfo, 0), // 主任务没有父任务
		QuestStartTime:       nowTs,
		QuestEndTime:         0,
		QuestRepeatLimitTime: questInfo.QuestRepeatLimitTime,
		QuestInfo:            questInfo,
		UpdateTs:             nowTs,
	}

	// 插入主任务到数据库
	mainQuestId, err := UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	if err != nil {
		return nil, fmt.Errorf("failed to add trainer quest: %v", err)
	}
	trainerQuest.Id = mainQuestId

	// 根据任务类型创建子任务
	err = createSubQuests(ctx, logger, tx, trainerQuest, questInfo)
	if err != nil {
		logger.Error("Failed to create sub quests: %v", err)
		return nil, err
	}

	logger.Info("Trainer %d accepted quest %d", trainer.Id, questId)
	return trainerQuest, nil
}

func tryUpdateQuestEnd(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainerQuest *MainServer.TrainerQuest) (bool, error) {
	change := false
	if trainerQuest.QuestStatus == MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		if trainerQuest.QuestInfo.QuestRepeatLimitTime > 0 && int64(trainerQuest.QuestInfo.QuestRepeatLimitTime)+trainerQuest.QuestStartTime < time.Now().Unix() {
			trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_timeout
			change = true
		}
		if trainerQuest.QuestEndTime > 0 && trainerQuest.QuestEndTime < time.Now().Unix() {
			trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_close
			change = true
		}
	}
	if change {
		// 更新数据库
		_, err := UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
		if err != nil {
			return change, fmt.Errorf("failed to update trainer quest: %v", err)
		}
	}
	return change, nil
}

func checkLastQuestIsEnd(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, trainerQuest *MainServer.TrainerQuest) bool {
	if trainerQuest.QuestStatus == MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		if trainerQuest.QuestInfo.QuestRepeatLimitTime > 0 && int64(trainerQuest.QuestInfo.QuestRepeatLimitTime)+trainerQuest.QuestStartTime < time.Now().Unix() {
			return true
		}
		if trainerQuest.QuestEndTime > 0 && trainerQuest.QuestEndTime < time.Now().Unix() {
			return true
		}
		return false
	}
	return true
}

// UpdateQuestProgress 更新任务进度
func UpdateQuestProgress(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32, progressKey string, progressValue int32) error {
	// 获取活跃的训练师任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, questId)
	if err != nil {
		return fmt.Errorf("active trainer quest not found: %v", err)
	}

	// 检查任务状态
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return fmt.Errorf("quest is not in progress")
	}

	// 更新进度
	err = updateQuestProgressData(trainerQuest, progressKey, progressValue)
	if err != nil {
		return fmt.Errorf("failed to update progress data: %v", err)
	}

	// 在新设计中，任务完成由客户端主动调用CompleteQuest，不需要自动检查
	// 只更新进度数据
	_, err = UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	if err != nil {
		return fmt.Errorf("failed to update trainer quest: %v", err)
	}

	logger.Info("Updated quest progress for trainer %d, quest %d: %s=%d", tid, questId, progressKey, progressValue)
	return nil
}

// updateQuestProgressData 更新任务进度数据
func updateQuestProgressData(trainerQuest *MainServer.TrainerQuest, progressKey string, progressValue int32) error {
	// 确保QuestCurrentInfo存在
	if trainerQuest.QuestCurrentInfo == nil {
		trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{
			QuestIdList:           []int32{},
			ParentQuestId:         0,
			QuestDefaultCondition: make(map[string]int32),
			QuestProgress:         make(map[string]int32),
			CompleteQuestMap:      make(map[int32]bool),
		}
	}

	// 确保QuestProgress存在
	if trainerQuest.QuestCurrentInfo.QuestProgress == nil {
		trainerQuest.QuestCurrentInfo.QuestProgress = make(map[string]int32)
	}

	// 更新进度值
	trainerQuest.QuestCurrentInfo.QuestProgress[progressKey] = progressValue

	// 更新时间戳
	trainerQuest.UpdateTs = time.Now().Unix()

	return nil
}

// CompleteQuest 完成任务 - 通过父任务ID和子任务ID完成（优化版本）
func CompleteQuestByParentAndId(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, parentDbId int32, subQuestDbId int32) error {
	//有父dbid
	if parentDbId != 0 {
		return CompleteSubQuest(ctx, logger, tx, trainer, parentDbId, subQuestDbId)
	}
	// 获取父任务（主任务）
	mainQuest, err := GetTrainerQuestById(ctx, logger, tx, trainer.Id, subQuestDbId)
	if err != nil {
		return fmt.Errorf("failed to get main quest: %v", err)
	}

	// 检查主任务状态
	if mainQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return runtime.NewError("main quest is not in accept status", 400)
	}
	completed, err := CheckQuestCanComplete(ctx, logger, tx, trainer, mainQuest)
	if err != nil {
		return err
	}
	if !completed {
		return runtime.NewError("quest can not complete", 400)
	}
	return completeQuestByDbid(ctx, logger, tx, trainer, mainQuest)
	// 获取子任务
	// 验证子任务在主任务的活跃列表中
	// found := false
	// for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
	// 	if dbId == subQuestDbId {
	// 		found = true
	// 		break
	// 	}
	// }
	// if !found {
	// 	return runtime.NewError("sub quest not found in main quest's active list", 400)
	// }

	// 完成子任务
	// mainQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward
	// mainQuest.QuestEndTime = time.Now().Unix()
	// _, err = UpsertTrainerQuest(ctx, logger, tx, subQuest)
	// if err != nil {
	// 	return fmt.Errorf("failed to update sub quest status: %v", err)
	// }

	// // 发放子任务奖励
	// if subQuest.QuestInfo != nil && subQuest.QuestInfo.QuestRewardInfo != nil {
	// 	claimQuestReward(ctx, logger, tx, tid, subQuest.QuestInfo.QuestRewardInfo)
	// }

	// 标记子任务为已完成
	// if mainQuest.QuestCurrentInfo.CompleteQuestMap == nil {
	// 	mainQuest.QuestCurrentInfo.CompleteQuestMap = make(map[int32]bool)
	// }
	// mainQuest.QuestCurrentInfo.CompleteQuestMap[subQuestDbId] = true

	// 检查是否需要推进任务
	// err = checkAndAdvanceQuest(ctx, logger, tx, mainQuest)
	// if err != nil {
	// 	return fmt.Errorf("failed to advance quest: %v", err)
	// }

	// logger.Info("Trainer %d completed sub quest %d (db_id: %d) of main quest %d", tid, subQuest.QuestId, subQuestDbId, parentDbId)
	// return nil
}

func CompleteSubQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, parentDbId int32, subQuestDbId int32) error {
	// 使用优化查询，一次性获取子任务并验证父子关系
	subQuest, err := GetSubQuestByParentAndId(ctx, logger, tx, trainer.Id, parentDbId, subQuestDbId)
	if err != nil {
		return fmt.Errorf("failed to get sub quest: %v", err)
	}

	// 检查子任务状态
	if subQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return runtime.NewError("sub quest is not in accept status", 400)
	}

	// 获取父任务（主任务）
	mainQuest, err := GetTrainerQuestById(ctx, logger, tx, trainer.Id, parentDbId)
	if err != nil {
		return fmt.Errorf("failed to get main quest: %v", err)
	}

	// 检查主任务状态
	if mainQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return runtime.NewError("main quest is not in accept status", 400)
	}

	// 验证子任务在主任务的活跃列表中
	// found := false
	// for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
	// 	if dbId == subQuestDbId {
	// 		found = true
	// 		break
	// 	}
	// }
	// if !found {
	// 	return runtime.NewError("sub quest not found in main quest's active list", 400)
	// }
	completeQuestByDbid(ctx, logger, tx, trainer, subQuest)
	// 完成子任务
	// subQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward
	// subQuest.QuestEndTime = time.Now().Unix()
	// _, err = UpsertTrainerQuest(ctx, logger, tx, subQuest)
	// if err != nil {
	// 	return fmt.Errorf("failed to update sub quest status: %v", err)
	// }

	// // 发放子任务奖励
	// if subQuest.QuestInfo != nil && subQuest.QuestInfo.QuestRewardInfo != nil {
	// 	claimQuestReward(ctx, logger, tx, trainer.Id, subQuest.QuestInfo.QuestRewardInfo)
	// }

	// 标记子任务为已完成
	if mainQuest.QuestCurrentInfo.CompleteQuestMap == nil {
		mainQuest.QuestCurrentInfo.CompleteQuestMap = make(map[int32]bool)
	}
	mainQuest.QuestCurrentInfo.CompleteQuestMap[subQuestDbId] = true

	// 检查是否需要推进任务
	err = checkAndAdvanceQuest(ctx, logger, tx, trainer, mainQuest)
	if err != nil {
		return fmt.Errorf("failed to advance quest: %v", err)
	}

	logger.Info("Trainer %d completed sub quest %d (db_id: %d) of main quest %d", trainer.Id, subQuest.QuestId, subQuestDbId, parentDbId)
	return nil
}
func completeQuestByDbid(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questInfo *MainServer.TrainerQuest) error {
	//有父dbid
	// 获取父任务（主任务）
	// questInfo, err := GetTrainerQuestById(ctx, logger, tx, trainer.Id, dbId)
	// if err != nil {
	// 	return fmt.Errorf("failed to get main quest: %v", err)
	// }

	// 检查主任务状态
	if questInfo.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return runtime.NewError("main quest is not in accept status", 400)
	}
	completed, err := CheckQuestCanComplete(ctx, logger, tx, trainer, questInfo)
	if err != nil {
		return err
	}
	if !completed {
		return runtime.NewError("quest can not complete", 400)
	}
	// 获取子任务
	// 验证子任务在主任务的活跃列表中
	// found := false
	// for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
	// 	if dbId == subQuestDbId {
	// 		found = true
	// 		break
	// 	}
	// }
	// if !found {
	// 	return runtime.NewError("sub quest not found in main quest's active list", 400)
	// }

	// 完成子任务
	questInfo.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward
	questInfo.QuestEndTime = time.Now().Unix()
	_, err = UpsertTrainerQuest(ctx, logger, tx, questInfo)
	if err != nil {
		return fmt.Errorf("failed to update sub quest status: %v", err)
	}

	// 发放子任务奖励
	if questInfo.QuestInfo != nil && questInfo.QuestInfo.QuestRewardInfo != nil {
		claimQuestReward(ctx, logger, tx, trainer, questInfo.QuestInfo.QuestRewardInfo)
	}

	// 标记子任务为已完成
	if questInfo.QuestCurrentInfo.CompleteQuestMap == nil {
		questInfo.QuestCurrentInfo.CompleteQuestMap = make(map[int32]bool)
	}
	// questInfo.QuestCurrentInfo.CompleteQuestMap[subQuestDbId] = true

	// 检查是否需要推进任务
	err = checkAndAdvanceQuest(ctx, logger, tx, trainer, questInfo)
	if err != nil {
		return fmt.Errorf("failed to advance quest: %v", err)
	}

	// logger.Info("Trainer %d completed sub quest %d (db_id: %d) of main quest %d", tid, subQuest.QuestId, subQuestDbId, parentDbId)
	return nil
}

// CompleteQuestById 完成任务 - 通过单个子任务数据库ID完成（兼容旧接口）
// func CompleteQuestById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, subQuestDbId int32) error {
// 	// 直接通过数据库ID获取子任务
// 	subQuest, err := GetTrainerQuestById(ctx, logger, tx, tid, subQuestDbId)
// 	if err != nil {
// 		return fmt.Errorf("failed to get sub quest: %v", err)
// 	}

// 	// 检查子任务状态
// 	if subQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
// 		return runtime.NewError("sub quest is not in accept status", 400)
// 	}

// 	// 如果是主任务本身（单一任务）
// 	if subQuest.QuestCurrentInfo.ParentQuestId == 0 {
// 		subQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward
// 		subQuest.QuestEndTime = time.Now().Unix()
// 		_, err = UpsertTrainerQuest(ctx, logger, tx, subQuest)
// 		if err != nil {
// 			return fmt.Errorf("failed to update main quest status: %v", err)
// 		}

// 		// 发放主任务奖励
// 		if subQuest.QuestInfo != nil && subQuest.QuestInfo.QuestRewardInfo != nil {
// 			claimQuestReward(ctx, logger, tx, tid, subQuest.QuestInfo.QuestRewardInfo)
// 		}

// 		logger.Info("Trainer %d completed main quest %d (db_id: %d)", tid, subQuest.QuestId, subQuestDbId)
// 		return nil
// 	}

// 	// 这是子任务，调用主完成函数
// 	return CompleteQuest(ctx, logger, tx, tid, subQuest.QuestCurrentInfo.ParentQuestId, subQuestDbId)
// }

// // CompleteQuestByConfigId 通过主任务ID和子任务配置ID完成任务（兼容旧接口）
// func CompleteQuestByConfigId(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, mainQuestId int32, subQuestConfigId int32) error {
// 	// 获取主任务
// 	mainQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, mainQuestId)
// 	if err != nil {
// 		return fmt.Errorf("main quest not found: %v", err)
// 	}

// 	// 查找要完成的子任务数据库ID
// 	var subQuestDbId int32 = 0
// 	for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
// 		subQuest, err := GetTrainerQuestById(ctx, logger, tx, tid, dbId)
// 		if err != nil {
// 			continue
// 		}
// 		if subQuest.QuestId == int64(subQuestConfigId) {
// 			subQuestDbId = dbId
// 			break
// 		}
// 	}

// 	if subQuestDbId == 0 {
// 		return runtime.NewError("sub quest not found in current quest list", 400)
// 	}

// 	// 获取子任务以获取父任务ID
// 	subQuest, err := GetTrainerQuestById(ctx, logger, tx, tid, subQuestDbId)
// 	if err != nil {
// 		return fmt.Errorf("failed to get sub quest: %v", err)
// 	}

// 	// 调用主完成函数
// 	return CompleteQuest(ctx, logger, tx, tid, subQuest.QuestCurrentInfo.ParentQuestId, subQuestDbId)
// }

// ClaimQuestReward 领取任务奖励
func claimQuestReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questRewardInfo *MainServer.QuestRewardInfo) error {
	// 获取活跃的训练师任务
	// trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, questId)
	// if err != nil {
	// 	return fmt.Errorf("active trainer quest not found: %v", err)
	// }

	// // 检查任务状态
	// if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_reward {
	// 	return fmt.Errorf("quest reward not available")
	// }

	// TODO: 发放奖励 - 需要避免循环导入
	err := GrantQuestReward(ctx, logger, tx, trainer, questRewardInfo)
	if err != nil {
		return fmt.Errorf("failed to grant quest reward: %v", err)
	}

	// 标记奖励已领取
	// trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward // TODO: 确认正确的枚举值

	// 更新数据库
	// _, err = UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	// if err != nil {
	// 	return fmt.Errorf("failed to update trainer quest: %v", err)
	// }

	// logger.Info("Trainer %d claimed reward for quest %d", tid, questId)
	return nil
}

// UpsertTrainerQuest 插入或更新训练师任务（参考UpsertTrainer模式）
func UpsertTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainerQuest *MainServer.TrainerQuest) (int64, error) {
	if trainerQuest == nil {
		return 0, fmt.Errorf("trainerQuest is nil")
	}
	if trainerQuest.QuestId <= 0 {
		return 0, fmt.Errorf("invalid quest id: %d", trainerQuest.QuestId)
	}

	nowTs := time.Now().Unix()

	// 如果ID小于等于0，生成新的ID
	var createTs int64
	if trainerQuest.Id <= 0 {
		err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", TableTrainerQuest)).Scan(&trainerQuest.Id)
		if err != nil {
			return 0, fmt.Errorf("failed to generate new id: %v", err)
		}
		createTs = nowTs
	} else {
		// 如果是更新，保持原有的创建时间
		createTs = trainerQuest.QuestStartTime // 使用QuestStartTime作为创建时间的替代
	}

	// 序列化JSONB字段
	var questCurrentInfoJSON, questInfoJSON []byte
	var err error

	if trainerQuest.QuestCurrentInfo != nil {
		questCurrentInfoJSON, err = json.Marshal(trainerQuest.QuestCurrentInfo)
		if err != nil {
			return 0, fmt.Errorf("failed to marshal quest_current_info: %v", err)
		}
	}

	if trainerQuest.QuestInfo != nil {
		questInfoJSON, err = json.Marshal(trainerQuest.QuestInfo)
		if err != nil {
			return 0, fmt.Errorf("failed to marshal quest_info: %v", err)
		}
	}

	query := fmt.Sprintf(`
		INSERT INTO %s (
			id, tid, quest_id, quest_type, quest_status, quest_current_info,
			quest_start_time, quest_end_time, quest_repeat_limit_time, quest_info,
			create_ts, update_ts
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		ON CONFLICT (id) DO UPDATE SET
			tid = EXCLUDED.tid,
			quest_id = EXCLUDED.quest_id,
			quest_type = EXCLUDED.quest_type,
			quest_status = EXCLUDED.quest_status,
			quest_current_info = EXCLUDED.quest_current_info,
			quest_start_time = EXCLUDED.quest_start_time,
			quest_end_time = EXCLUDED.quest_end_time,
			quest_repeat_limit_time = EXCLUDED.quest_repeat_limit_time,
			quest_info = EXCLUDED.quest_info,
			update_ts = EXCLUDED.update_ts
		RETURNING id
	`, TableTrainerQuest)

	var id int64
	err = tx.QueryRowContext(ctx, query,
		trainerQuest.Id, trainerQuest.Tid, trainerQuest.QuestId, int32(trainerQuest.QuestType),
		int32(trainerQuest.QuestStatus), questCurrentInfoJSON,
		trainerQuest.QuestStartTime, trainerQuest.QuestEndTime,
		trainerQuest.QuestRepeatLimitTime, questInfoJSON, createTs, nowTs,
	).Scan(&id)

	if err != nil {
		logger.Error("Failed to upsert trainer quest: %v", err)
		return 0, err
	}

	logger.Debug("Trainer quest upsert successful: id=%d, tid=%d, quest_id=%d", id, trainerQuest.Tid, trainerQuest.QuestId)
	return id, nil
}

// TrainerQuestFilter 训练师任务查询过滤器
type TrainerQuestFilter struct {
	Id               *int32                          // 数据库ID
	Tid              *int64                          // 训练师ID
	QuestId          *int32                          // 任务ID
	QuestStatus      []MainServer.TrainerQuestStatus // 任务状态列表
	QuestType        *MainServer.QuestType           // 任务类型
	StartTimeAfter   *int64                          // 开始时间之后
	StartTimeBefore  *int64                          // 开始时间之前
	UpdateTimeAfter  *int64                          // 更新时间之后
	UpdateTimeBefore *int64                          // 更新时间之前
	OrderBy          string                          // 排序字段 (update_ts, quest_start_time, id)
	OrderDesc        bool                            // 是否降序
	Limit            *int                            // 限制数量
}

// QueryTrainerQuests 通用的训练师任务查询函数
func QueryTrainerQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, filter *TrainerQuestFilter) ([]*MainServer.TrainerQuest, error) {
	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, quest_id, quest_type, quest_status, quest_current_info,
		       quest_start_time, quest_end_time, quest_repeat_limit_time, quest_info,
		       create_ts, update_ts
		FROM %s WHERE 1=1`, TableTrainerQuest)

	var args []interface{}
	argIndex := 1

	// 添加过滤条件
	if filter.Id != nil {
		query += fmt.Sprintf(" AND id = $%d", argIndex)
		args = append(args, *filter.Id)
		argIndex++
	}

	if filter.Tid != nil {
		query += fmt.Sprintf(" AND tid = $%d", argIndex)
		args = append(args, *filter.Tid)
		argIndex++
	}

	if filter.QuestId != nil {
		query += fmt.Sprintf(" AND quest_id = $%d", argIndex)
		args = append(args, *filter.QuestId)
		argIndex++
	}

	if len(filter.QuestStatus) > 0 {
		statusPlaceholders := make([]string, len(filter.QuestStatus))
		for i, status := range filter.QuestStatus {
			statusPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, int32(status))
			argIndex++
		}
		query += fmt.Sprintf(" AND quest_status IN (%s)", strings.Join(statusPlaceholders, ","))
	}

	if filter.QuestType != nil {
		query += fmt.Sprintf(" AND quest_type = $%d", argIndex)
		args = append(args, int32(*filter.QuestType))
		argIndex++
	}

	if filter.StartTimeAfter != nil {
		query += fmt.Sprintf(" AND quest_start_time > $%d", argIndex)
		args = append(args, *filter.StartTimeAfter)
		argIndex++
	}

	if filter.StartTimeBefore != nil {
		query += fmt.Sprintf(" AND quest_start_time < $%d", argIndex)
		args = append(args, *filter.StartTimeBefore)
		argIndex++
	}

	if filter.UpdateTimeAfter != nil {
		query += fmt.Sprintf(" AND update_ts > $%d", argIndex)
		args = append(args, *filter.UpdateTimeAfter)
		argIndex++
	}

	if filter.UpdateTimeBefore != nil {
		query += fmt.Sprintf(" AND update_ts < $%d", argIndex)
		args = append(args, *filter.UpdateTimeBefore)
		argIndex++
	}

	// 添加排序
	orderBy := "update_ts" // 默认按更新时间排序
	if filter.OrderBy != "" {
		switch filter.OrderBy {
		case "update_ts", "quest_start_time", "id", "create_ts":
			orderBy = filter.OrderBy
		default:
			logger.Warn("Invalid order by field: %s, using default: update_ts", filter.OrderBy)
		}
	}

	orderDirection := "DESC"
	if !filter.OrderDesc {
		orderDirection = "ASC"
	}
	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, orderDirection)

	// 添加限制
	if filter.Limit != nil && *filter.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filter.Limit)
	}

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		logger.Error("Failed to query trainer quests: %v", err)
		return nil, err
	}
	defer rows.Close()

	var trainerQuests []*MainServer.TrainerQuest
	for rows.Next() {
		trainerQuest := &MainServer.TrainerQuest{}
		var questStatus, questType int32
		var questCurrentInfoJSON, questInfoJSON []byte
		var createTs int64

		err := rows.Scan(
			&trainerQuest.Id, &trainerQuest.QuestId, &questType,
			&questStatus, &questCurrentInfoJSON,
			&trainerQuest.QuestStartTime, &trainerQuest.QuestEndTime, &trainerQuest.QuestRepeatLimitTime,
			&questInfoJSON, &createTs, &trainerQuest.UpdateTs,
		)
		if err != nil {
			logger.Error("Failed to scan trainer quest: %v", err)
			continue
		}

		// 设置枚举类型
		trainerQuest.QuestStatus = MainServer.TrainerQuestStatus(questStatus)
		trainerQuest.QuestType = MainServer.QuestType(questType)

		// 反序列化JSONB字段
		if questCurrentInfoJSON != nil {
			trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{}
			if err := json.Unmarshal(questCurrentInfoJSON, trainerQuest.QuestCurrentInfo); err != nil {
				logger.Error("Failed to unmarshal quest_current_info: %v", err)
				trainerQuest.QuestCurrentInfo = nil
			}
		}

		if questInfoJSON != nil {
			trainerQuest.QuestInfo = &MainServer.QuestInfo{}
			if err := json.Unmarshal(questInfoJSON, trainerQuest.QuestInfo); err != nil {
				logger.Error("Failed to unmarshal quest_info: %v", err)
				trainerQuest.QuestInfo = nil
			}
		}

		trainerQuests = append(trainerQuests, trainerQuest)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error iterating trainer quest rows: %v", err)
		return nil, err
	}

	return trainerQuests, nil
}

// GetTrainerQuest 获取训练师任务信息（获取最新的一个）
func GetTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (*MainServer.TrainerQuest, error) {
	limit := 1
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		QuestId:   &questId,
		OrderBy:   "update_ts",
		OrderDesc: true,
		Limit:     &limit,
	}

	quests, err := QueryTrainerQuests(ctx, logger, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(quests) == 0 {
		return nil, fmt.Errorf("trainer quest not found")
	}

	return quests[0], nil
}

// GetActiveTrainerQuest 获取训练师特定任务的活跃记录（未完成的最新记录）
func GetActiveTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (*MainServer.TrainerQuest, error) {
	limit := 1
	filter := &TrainerQuestFilter{
		Tid:     &tid,
		QuestId: &questId,
		QuestStatus: []MainServer.TrainerQuestStatus{
			MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
			MainServer.TrainerQuestStatus_TrainerQuestStatus_finish,
		},
		OrderBy:   "update_ts",
		OrderDesc: true,
		Limit:     &limit,
	}

	quests, err := QueryTrainerQuests(ctx, logger, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(quests) == 0 {
		return nil, fmt.Errorf("active trainer quest not found")
	}

	return quests[0], nil
}

// GetTrainerQuestHistory 获取训练师任务历史记录
func GetTrainerQuestHistory(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		QuestId:   &questId,
		OrderBy:   "update_ts",
		OrderDesc: true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestsByTime 获取指定时间范围内的训练师任务记录
// 查询条件: startTimeTs - interval < quest_start_time < startTimeTs
func GetTrainerQuestsByTime(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32, startTimeTs int64, interval int64) ([]*MainServer.TrainerQuest, error) {
	minTimeTs := startTimeTs - interval

	filter := &TrainerQuestFilter{
		Tid:             &tid,
		QuestId:         &questId,
		StartTimeAfter:  &minTimeTs,
		StartTimeBefore: &startTimeTs,
		OrderBy:         "quest_start_time",
		OrderDesc:       true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// CheckQuestCanComplete 检查任务是否可以完成
func CheckQuestCanComplete(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, trainerQuest *MainServer.TrainerQuest) (bool, error) {
	// 获取活跃的训练师任务
	// trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, trainer.Id, questId)
	// if err != nil {
	// 	return false, fmt.Errorf("active trainer quest not found: %v", err)
	// }

	// 检查任务状态
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return false, fmt.Errorf("quest is not in progress")
	}

	// 检查完成条件
	if trainerQuest.QuestInfo != nil && trainerQuest.QuestInfo.QuestCompleteInfo != nil {
		// 从quest_current_info中获取进度数据
		progress := make(map[string]interface{})
		if trainerQuest.QuestCurrentInfo != nil && trainerQuest.QuestCurrentInfo.QuestProgress != nil {
			for k, v := range trainerQuest.QuestCurrentInfo.QuestProgress {
				progress[k] = v
			}
		}

		completed, err := CheckQuestCompleteConditions(ctx, logger, tx, trainer, trainerQuest.QuestInfo.QuestCompleteInfo, progress)
		if err != nil {
			return false, fmt.Errorf("failed to check complete conditions: %v", err)
		}
		return completed, nil
	}

	// 如果没有完成条件，默认可以完成
	return true, nil
}

// 便利查询函数

// GetTrainerQuestsByStatus 根据状态获取训练师任务
func GetTrainerQuestsByStatus(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, statuses []MainServer.TrainerQuestStatus) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:         &tid,
		QuestStatus: statuses,
		OrderBy:     "update_ts",
		OrderDesc:   true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerActiveQuests 获取训练师所有活跃任务
func GetTrainerActiveQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerQuest, error) {
	return GetTrainerQuestsByStatus(ctx, logger, tx, tid, []MainServer.TrainerQuestStatus{
		MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
	})
}

// GetTrainerCompletedQuests 获取训练师已完成任务
func GetTrainerCompletedQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, limit *int) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid: &tid,
		QuestStatus: []MainServer.TrainerQuestStatus{
			MainServer.TrainerQuestStatus_TrainerQuestStatus_reward,
		},
		OrderBy:   "update_ts",
		OrderDesc: true,
		Limit:     limit,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestsByType 根据任务类型获取训练师任务
func GetTrainerQuestsByType(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questType MainServer.QuestType) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		QuestType: &questType,
		OrderBy:   "update_ts",
		OrderDesc: true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetRecentTrainerQuests 获取训练师最近的任务记录
func GetRecentTrainerQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, hours int64, limit *int) ([]*MainServer.TrainerQuest, error) {
	recentTime := time.Now().Unix() - (hours * 3600)

	filter := &TrainerQuestFilter{
		Tid:             &tid,
		UpdateTimeAfter: &recentTime,
		OrderBy:         "update_ts",
		OrderDesc:       true,
		Limit:           limit,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestList 获取训练师任务列表（兼容旧接口）
func GetTrainerQuestList(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		OrderBy:   "update_ts",
		OrderDesc: true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestById 通过数据库ID获取训练师任务
func GetTrainerQuestById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, dbId int32) (*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid: &tid,
		Id:  &dbId, // 需要在TrainerQuestFilter中添加Id字段
	}

	quests, err := QueryTrainerQuests(ctx, logger, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(quests) == 0 {
		return nil, fmt.Errorf("trainer quest with id %d not found", dbId)
	}

	return quests[0], nil
}

// GetSubQuestByParentAndId 通过父任务ID和子任务ID获取子任务
func GetSubQuestByParentAndId(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, parentDbId int32, subQuestDbId int32) (*MainServer.TrainerQuest, error) {
	// 构建查询语句，同时验证父子关系
	query := fmt.Sprintf(`
		SELECT id, quest_id, quest_type, quest_status, quest_current_info,
		       quest_start_time, quest_end_time, quest_repeat_limit_time, quest_info,
		       create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND id = $2 AND ((quest_current_info->>'parent_quest_id')::int) = $3
	`, TableTrainerQuest)

	rows, err := tx.QueryContext(ctx, query, tid, subQuestDbId, parentDbId)
	if err != nil {
		logger.Error("Failed to query sub quest by parent and id: %v", err)
		return nil, err
	}
	defer rows.Close()

	if !rows.Next() {
		return nil, fmt.Errorf("sub quest with id %d and parent %d not found", subQuestDbId, parentDbId)
	}

	trainerQuest := &MainServer.TrainerQuest{}
	var questStatus, questType int32
	var questCurrentInfoJSON, questInfoJSON []byte
	var createTs int64

	err = rows.Scan(
		&trainerQuest.Id, &trainerQuest.QuestId, &questType,
		&questStatus, &questCurrentInfoJSON,
		&trainerQuest.QuestStartTime, &trainerQuest.QuestEndTime, &trainerQuest.QuestRepeatLimitTime,
		&questInfoJSON, &createTs, &trainerQuest.UpdateTs,
	)
	if err != nil {
		logger.Error("Failed to scan sub quest: %v", err)
		return nil, err
	}

	// 设置枚举类型
	trainerQuest.QuestStatus = MainServer.TrainerQuestStatus(questStatus)
	trainerQuest.QuestType = MainServer.QuestType(questType)
	trainerQuest.Tid = tid

	// 反序列化JSONB字段
	if questCurrentInfoJSON != nil {
		trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{}
		if err := json.Unmarshal(questCurrentInfoJSON, trainerQuest.QuestCurrentInfo); err != nil {
			logger.Error("Failed to unmarshal quest_current_info: %v", err)
			trainerQuest.QuestCurrentInfo = nil
		}
	}

	if questInfoJSON != nil {
		trainerQuest.QuestInfo = &MainServer.QuestInfo{}
		if err := json.Unmarshal(questInfoJSON, trainerQuest.QuestInfo); err != nil {
			logger.Error("Failed to unmarshal quest_info: %v", err)
			trainerQuest.QuestInfo = nil
		}
	}

	return trainerQuest, nil
}

// checkAndAdvanceQuest 检查并推进任务
func checkAndAdvanceQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, mainQuest *MainServer.TrainerQuest) error {
	// 检查是否所有当前子任务都已完成
	allCompleted := true
	for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
		if !mainQuest.QuestCurrentInfo.CompleteQuestMap[dbId] {
			allCompleted = false
			break
		}
	}

	if !allCompleted {
		// 还有未完成的子任务，更新主任务状态即可
		_, err := UpsertTrainerQuest(ctx, logger, tx, mainQuest)
		return err
	}

	// 所有当前子任务都已完成，检查任务类型
	if mainQuest.QuestInfo.LinearQuests != nil && len(mainQuest.QuestInfo.LinearQuests.QuestIds) > 0 {
		// 线性任务：推进到下一个任务
		return advanceLinearQuest(ctx, logger, tx, trainer, mainQuest)
	} else {
		// 并行任务或单一任务：完成整个主任务
		return completeMainQuest(ctx, logger, tx, trainer, mainQuest)
	}
}

// advanceLinearQuest 推进线性任务到下一个子任务
func advanceLinearQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, mainQuest *MainServer.TrainerQuest) error {
	linearQuests := mainQuest.QuestInfo.LinearQuests.QuestIds

	// 找到当前完成的任务在线性任务中的位置
	currentIndex := -1
	completedQuestId := int32(0)

	// 从已完成的子任务中找到最新完成的那个
	for dbId, completed := range mainQuest.QuestCurrentInfo.CompleteQuestMap {
		if completed {
			subQuest, err := GetTrainerQuestById(ctx, logger, tx, mainQuest.Tid, dbId)
			if err == nil {
				for i, questId := range linearQuests {
					if questId == int32(subQuest.QuestId) {
						if i > currentIndex {
							currentIndex = i
							completedQuestId = questId
						}
						break
					}
				}
			}
		}
	}

	// 检查是否还有下一个任务
	nextIndex := currentIndex + 1
	if nextIndex >= len(linearQuests) {
		// 线性任务全部完成
		return completeMainQuest(ctx, logger, tx, trainer, mainQuest)
	}

	// 创建下一个子任务
	nextQuestId := linearQuests[nextIndex]
	subQuestId, err := createSingleSubQuest(ctx, logger, tx, mainQuest, nextQuestId)
	if err != nil {
		return fmt.Errorf("failed to create next sub quest: %v", err)
	}

	// 更新主任务的quest_id_list，添加新的子任务
	mainQuest.QuestCurrentInfo.QuestIdList = append(mainQuest.QuestCurrentInfo.QuestIdList, subQuestId)

	_, err = UpsertTrainerQuest(ctx, logger, tx, mainQuest)
	if err != nil {
		return fmt.Errorf("failed to update main quest for next linear quest: %v", err)
	}

	logger.Info("Advanced linear quest %d from quest %d to next sub quest %d (db_id: %d)",
		mainQuest.Id, completedQuestId, nextQuestId, subQuestId)
	return nil
}

// completeMainQuest 完成整个主任务
func completeMainQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, mainQuest *MainServer.TrainerQuest) error {
	// 标记主任务为已完成
	mainQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward
	mainQuest.QuestEndTime = time.Now().Unix()

	// 发放主任务奖励
	if mainQuest.QuestInfo != nil && mainQuest.QuestInfo.QuestRewardInfo != nil {
		claimQuestReward(ctx, logger, tx, trainer, mainQuest.QuestInfo.QuestRewardInfo)
	}

	_, err := UpsertTrainerQuest(ctx, logger, tx, mainQuest)
	if err != nil {
		return fmt.Errorf("failed to complete main quest: %v", err)
	}

	logger.Info("Completed main quest %d for trainer %d", mainQuest.Id, mainQuest.Tid)
	return nil
}

// 便利函数：处理TrainerQuestCurrentInfo

// GetCurrentQuestFromTrainerQuest 从TrainerQuest中获取当前任务信息
// 在新设计中，当前任务信息存储在子任务中，这个函数返回主任务信息
func GetCurrentQuestFromTrainerQuest(trainerQuest *MainServer.TrainerQuest) *MainServer.QuestInfo {
	if trainerQuest == nil {
		return nil
	}
	return trainerQuest.QuestInfo
}

// GetQuestProgressFromTrainerQuest 从TrainerQuest中获取任务进度
func GetQuestProgressFromTrainerQuest(trainerQuest *MainServer.TrainerQuest) map[string]int32 {
	if trainerQuest == nil || trainerQuest.QuestCurrentInfo == nil {
		return make(map[string]int32)
	}
	if trainerQuest.QuestCurrentInfo.QuestProgress == nil {
		return make(map[string]int32)
	}
	return trainerQuest.QuestCurrentInfo.QuestProgress
}

// GetQuestDefaultConditionFromTrainerQuest 从TrainerQuest中获取默认完成条件
func GetQuestDefaultConditionFromTrainerQuest(trainerQuest *MainServer.TrainerQuest) map[string]int32 {
	if trainerQuest == nil || trainerQuest.QuestCurrentInfo == nil {
		return make(map[string]int32)
	}
	if trainerQuest.QuestCurrentInfo.QuestDefaultCondition == nil {
		return make(map[string]int32)
	}
	return trainerQuest.QuestCurrentInfo.QuestDefaultCondition
}

// UpdateQuestCurrentInfo 更新TrainerQuest的当前信息
func UpdateQuestCurrentInfo(trainerQuest *MainServer.TrainerQuest, questIdList []int32, defaultConditions map[string]int32, progress map[string]int32) {
	if trainerQuest == nil {
		return
	}

	if trainerQuest.QuestCurrentInfo == nil {
		trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{
			QuestIdList:           []int32{},
			ParentQuestId:         0,
			QuestDefaultCondition: make(map[string]int32),
			QuestProgress:         make(map[string]int32),
			CompleteQuestMap:      make(map[int32]bool),
		}
	}

	if questIdList != nil {
		trainerQuest.QuestCurrentInfo.QuestIdList = questIdList
	}

	if defaultConditions != nil {
		trainerQuest.QuestCurrentInfo.QuestDefaultCondition = defaultConditions
	}

	if progress != nil {
		trainerQuest.QuestCurrentInfo.QuestProgress = progress
	}

	// 更新时间戳
	trainerQuest.UpdateTs = time.Now().Unix()
}

// 便利函数：处理QuestCancelInfo

// CanCancelQuest 检查任务是否可以取消
func CanCancelQuest(trainerQuest *MainServer.TrainerQuest) bool {
	if trainerQuest == nil || trainerQuest.QuestInfo == nil || trainerQuest.QuestInfo.QuestCancelInfo == nil {
		return false
	}

	// 检查任务状态是否允许取消
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return false
	}

	// 检查取消间隔时间
	cancelInterval := trainerQuest.QuestInfo.QuestCancelInfo.CancelInterval
	if cancelInterval <= 0 {
		return false // 不允许取消
	}

	// 检查是否已过取消间隔时间
	currentTime := time.Now().Unix()
	timeSinceStart := currentTime - trainerQuest.QuestStartTime

	return timeSinceStart >= int64(cancelInterval)
}

// GetCancelAcceptInterval 获取取消后再次接受的间隔时间
func GetCancelAcceptInterval(questInfo *MainServer.QuestInfo) int32 {
	if questInfo == nil || questInfo.QuestCancelInfo == nil {
		return 0
	}
	return questInfo.QuestCancelInfo.CancelAcceptInterval
}

// CanAcceptQuestAfterCancel 检查取消任务后是否可以再次接受
func CanAcceptQuestAfterCancel(lastCancelTime int64, questInfo *MainServer.QuestInfo) bool {
	if questInfo == nil || questInfo.QuestCancelInfo == nil {
		return true // 没有取消信息，默认可以接受
	}

	cancelAcceptInterval := questInfo.QuestCancelInfo.CancelAcceptInterval
	if cancelAcceptInterval <= 0 {
		return true // 没有间隔限制
	}

	currentTime := time.Now().Unix()
	timeSinceCancel := currentTime - lastCancelTime

	return timeSinceCancel >= int64(cancelAcceptInterval)
}

// GetQuestCancelInfo 从TrainerQuest中获取取消信息
func GetQuestCancelInfo(trainerQuest *MainServer.TrainerQuest) *MainServer.QuestCancelInfo {
	if trainerQuest == nil || trainerQuest.QuestInfo == nil {
		return nil
	}
	return trainerQuest.QuestInfo.QuestCancelInfo
}

// 新增便利函数：获取活跃子任务

// GetActiveSubQuests 获取主任务的所有活跃子任务
func GetActiveSubQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, mainQuestId int32) ([]*MainServer.TrainerQuest, error) {
	// 获取主任务
	mainQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, mainQuestId)
	if err != nil {
		return nil, fmt.Errorf("main quest not found: %v", err)
	}

	var activeSubQuests []*MainServer.TrainerQuest
	for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
		// 检查是否已完成
		if mainQuest.QuestCurrentInfo.CompleteQuestMap[dbId] {
			continue // 跳过已完成的子任务
		}

		subQuest, err := GetTrainerQuestById(ctx, logger, tx, tid, dbId)
		if err != nil {
			logger.Error("Failed to get sub quest %d: %v", dbId, err)
			continue
		}

		// 只返回活跃状态的子任务
		if subQuest.QuestStatus == MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
			activeSubQuests = append(activeSubQuests, subQuest)
		}
	}

	return activeSubQuests, nil
}

// GetSubQuestByConfigId 通过配置ID获取子任务
func GetSubQuestByConfigId(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, mainQuestId int32, subQuestConfigId int32) (*MainServer.TrainerQuest, error) {
	// 获取主任务
	mainQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, mainQuestId)
	if err != nil {
		return nil, fmt.Errorf("main quest not found: %v", err)
	}

	// 查找指定配置ID的子任务
	for _, dbId := range mainQuest.QuestCurrentInfo.QuestIdList {
		subQuest, err := GetTrainerQuestById(ctx, logger, tx, tid, dbId)
		if err != nil {
			continue
		}
		if subQuest.QuestId == int64(subQuestConfigId) {
			return subQuest, nil
		}
	}

	return nil, fmt.Errorf("sub quest with config id %d not found", subQuestConfigId)
}

// IsSubQuestCompleted 检查子任务是否已完成
func IsSubQuestCompleted(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, mainQuestId int32, subQuestConfigId int32) (bool, error) {
	subQuest, err := GetSubQuestByConfigId(ctx, logger, tx, tid, mainQuestId, subQuestConfigId)
	if err != nil {
		return false, err
	}

	return subQuest.QuestStatus == MainServer.TrainerQuestStatus_TrainerQuestStatus_reward, nil
}
