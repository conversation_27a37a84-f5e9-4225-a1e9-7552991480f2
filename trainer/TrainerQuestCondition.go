package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"go-nakama-poke/inventory"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// QuestConditionManager 任务条件管理器（从内存中读取配置）
var QuestUnlockConditions map[int32]*MainServer.QuestUnlockInfo
var QuestCompleteConditions map[int32]*MainServer.QuestCompleteInfo

// InitTrainerQuestConditions 初始化训练师任务条件（从JSON配置文件加载到内存）
func InitTrainerQuestConditions() {
	// 这里应该从JSON配置文件加载条件数据到内存
	// 目前使用空的map，实际使用时需要加载配置
	QuestUnlockConditions = make(map[int32]*MainServer.QuestUnlockInfo)
	QuestCompleteConditions = make(map[int32]*MainServer.QuestCompleteInfo)
}

// CheckQuestUnlockConditions 检查任务解锁条件
func CheckQuestUnlockConditions(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, unlockInfo *MainServer.QuestUnlockInfo) (bool, error) {
	if unlockInfo == nil {
		return true, nil // 无解锁条件
	}

	// 检查每个解锁条件
	for _, condition := range unlockInfo.QuestUnlockConditions {
		passed, err := checkSingleUnlockCondition(ctx, logger, tx, trainer, condition.QuestUnlockType, condition)
		if err != nil {
			return false, err
		}
		if !passed {
			return false, nil // 任何一个条件不满足都返回false
		}
	}

	return true, nil
}

// checkSingleUnlockCondition 检查单个解锁条件
func checkSingleUnlockCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, unlockType MainServer.QuestUnlockType, condition *MainServer.QuestUnlockConditionInfo) (bool, error) {
	switch unlockType {
	case MainServer.QuestUnlockType_QuestUnlockType_none:
		// 无条件，直接通过
		return true, nil

	// case MainServer.QuestUnlockType_QuestUnlockType_level:
	//	// 检查训练师等级
	//	// TODO: 需要确认Trainer结构中等级字段的正确名称
	//	logger.Info("Level unlock condition check not implemented yet")
	//	return true, nil

	case MainServer.QuestUnlockType_QuestUnlockType_quest:
		// 检查前置任务是否完成
		questId, err := strconv.ParseInt(condition.QuestCondition.ConditionNameId, 10, 32)
		if err != nil {
			return false, fmt.Errorf("invalid quest id: %s", condition.QuestCondition.ConditionNameId)
		}
		return IsQuestCompleted(ctx, logger, tx, trainer, int32(questId))

	case MainServer.QuestUnlockType_QuestUnlockType_item:
		// 检查道具数量
		// logger.Info("Item unlock condition check not implemented yet")
		// hasEnough, err := inventory.CheckItemQuantity(ctx, tx, trainer.Id, condition.QuestCondition.ConditionNameId, int(condition.QuestCondition.ConditionCount))
		// if err != nil {
		// 	return false, fmt.Errorf("failed to check item quantity: %v", err)
		// }
		// if ok, err := checkItemCondition(ctx, logger, tx, trainer, condition.QuestCondition); !ok {
		// 	return false, err
		// }
		return checkItemCondition(ctx, logger, tx, trainer, condition.QuestCondition)
	case MainServer.QuestUnlockType_QuestUnlockType_money:
		return checkCoinCondition(ctx, logger, tx, trainer, condition.QuestCondition)
		// 检查金钱
		// requiredMoney := int64(condition.QuestCondition.ConditionCount)
		// if condition.QuestCondition.IsUsed {
		// 	err := expendCoin(ctx, logger, tx, trainer, requiredMoney)
		// 	if err != nil {
		// 		return false, fmt.Errorf("failed to expend money: %v", err)
		// 	}
		// 	return true, nil
		// }
		// return trainer.Coin >= requiredMoney, nil

	case MainServer.QuestUnlockType_QuestUnlockType_poke:
		return checkPokeCondition(ctx, logger, tx, trainer, condition.QuestCondition)

	case MainServer.QuestUnlockType_QuestUnlockType_title:
		// 检查是否拥有指定称号
		return checkTrainerHasTitle(trainer, condition.QuestCondition.ConditionNameId), nil

	case MainServer.QuestUnlockType_QuestUnlockType_time:
		// 检查时间条件
		return checkTimeCondition(condition.QuestCondition.JsonValue), nil

	case MainServer.QuestUnlockType_QuestUnlockType_team:
		// 检查队伍条件
		if condition.QuestCondition.ConditionNameId == "" {
			return true, nil
		}
		teamType, err := strconv.ParseInt(condition.QuestCondition.ConditionNameId, 10, 32)
		if err != nil {
			return false, fmt.Errorf("invalid team type: %s", condition.QuestCondition.ConditionNameId)
		}
		return int32(trainer.Team) == int32(teamType), nil

	default:
		logger.Error("Unknown unlock type: %v", unlockType)
		return false, fmt.Errorf("unknown unlock type")
	}
}

// CheckQuestCompleteConditions 检查任务完成条件
func CheckQuestCompleteConditions(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, completeInfo *MainServer.QuestCompleteInfo, progress map[string]interface{}) (bool, error) {
	if completeInfo == nil {
		return true, nil // 无完成条件
	}

	// 检查每个完成条件
	for _, condition := range completeInfo.QuestCompleteConditions {
		passed, err := checkSingleCompleteCondition(ctx, logger, tx, trainer, condition.QuestCompleteType, condition, progress)
		if err != nil {
			return false, err
		}
		if !passed {
			return false, nil // 任何一个条件不满足都返回false
		}
	}

	return true, nil
}

// checkSingleCompleteCondition 检查单个完成条件
func checkSingleCompleteCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, completeType MainServer.QuestCompleteType, condition *MainServer.QuestCompleteConditionInfo, progress map[string]interface{}) (bool, error) {
	switch completeType {
	case MainServer.QuestCompleteType_QuestCompleteType_battle_poke:
		// 检查击败指定宝可梦的数量
		key := fmt.Sprintf("battle_poke_%s", condition.QuestCondition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.QuestCondition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_battle_npc:
		// 检查击败指定NPC的数量
		key := fmt.Sprintf("battle_npc_%s", condition.QuestCondition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.QuestCondition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_collect_item:
		// 检查收集道具数量
		key := fmt.Sprintf("collect_item_%s", condition.QuestCondition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.QuestCondition.ConditionCount, nil

	// case MainServer.QuestCompleteType_QuestCompleteType_catch_poke:
	//	// 检查捕获宝可梦数量
	//	key := fmt.Sprintf("catch_poke_%s", condition.QuestCondition.ConditionNameId)
	//	count := getProgressCount(progress, key)
	//	return count >= condition.QuestCondition.ConditionCount, nil

	// case MainServer.QuestCompleteType_QuestCompleteType_visit_location:
	//	// 检查访问地点
	//	key := fmt.Sprintf("visit_location_%s", condition.QuestCondition.ConditionNameId)
	//	count := getProgressCount(progress, key)
	//	return count >= condition.QuestCondition.ConditionCount, nil

	// case MainServer.QuestCompleteType_QuestCompleteType_use_item:
	//	// 检查使用道具数量
	//	key := fmt.Sprintf("use_item_%s", condition.QuestCondition.ConditionNameId)
	//	count := getProgressCount(progress, key)
	//	return count >= condition.QuestCondition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_custom:
		// 自定义条件
		return checkCustomCondition(condition.QuestCondition.JsonValue, progress), nil

	default:
		logger.Error("Unknown complete type: %v", completeType)
		return false, fmt.Errorf("unknown complete type")
	}
}

// IsQuestCompleted 检查任务是否已完成
func IsQuestCompleted(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questId int32) (bool, error) {
	// 查询训练师是否完成过该任务
	query := fmt.Sprintf(`
		SELECT COUNT(*) FROM %s 
		WHERE tid = $1 AND quest_id = $2 AND quest_status = $3
	`, TableTrainerQuest)

	var count int
	err := tx.QueryRowContext(ctx, query, trainer.Id, questId, int32(MainServer.TrainerQuestStatus_TrainerQuestStatus_reward)).Scan(&count)
	if err != nil {
		logger.Error("Failed to check quest completion: %v", err)
		return false, err
	}

	return count > 0, nil
}
func checkPokeCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, condition *MainServer.QuestConditionInfo) (bool, error) {

	// 检查是否拥有指定宝可梦 TODO: 添加的宝可梦
	if condition.IsUsed {
		err := releaseQuestPoke(ctx, logger, tx, trainer, condition.ConditionNameId)
		if err != nil {
			return false, fmt.Errorf("failed to release poke: %v", err)
		}
		return true, nil
	}
	return checkTrainerHasPoke(trainer, condition.ConditionNameId), nil
}
func checkItemCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, condition *MainServer.QuestConditionInfo) (bool, error) {
	if condition.IsUsed {
		return inventory.RemoveItem(ctx, tx, trainer.Id, condition.ConditionNameId, int(condition.ConditionCount)) == nil, nil
	} else {
		return inventory.CheckItemQuantity(ctx, tx, trainer.Id, condition.ConditionNameId, int(condition.ConditionCount))
	}
}
func checkCoinCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, condition *MainServer.QuestConditionInfo) (bool, error) {
	// 检查训练师的金币是否足够
	requiredMoney := int64(condition.ConditionCount)
	if condition.IsUsed {
		err := expendCoin(ctx, logger, tx, trainer, requiredMoney)
		if err != nil {
			return false, fmt.Errorf("failed to expend money: %v", err)
		}
		return true, nil
	}
	return trainer.Coin >= requiredMoney, nil
}

// checkTrainerHasPoke 检查训练师是否拥有指定宝可梦
func checkTrainerHasPoke(trainer *MainServer.Trainer, pokeName string) bool {
	// 检查训练师的PokeIds中是否包含指定宝可梦
	pokes, exists := tool.GetTrainerAroundPokes(trainer.Id)
	if !exists {
		return false // 如果没有宝可梦数据，直接返回false
	}
	for _, poke := range pokes {
		if poke.Name == pokeName {
			return true // 找到匹配的宝可梦，返回true
		}
	}
	return false // 占位实现
}
func releaseQuestPoke(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, pokeName string) error {
	pokes, exists := tool.GetTrainerAroundPokes(trainer.Id)
	if !exists {
		return runtime.NewError("Trainer has no pokes", 400)
	}
	loc := -1
	for i, poke := range pokes {
		if poke.Name == pokeName {
			loc = i
			break // 找到匹配的宝可梦，退出循环
		}
	}
	if loc == -1 {
		return runtime.NewError("Trainer does not have the specified poke", 400)
	}
	_, err := ReleasePoke(ctx, logger, tx, trainer, MainServer.PokeBoxType_around, 0, int32(loc)) // 假设使用第一个盒子
	return err
}

// checkTrainerHasTitle 检查训练师是否拥有指定称号
func checkTrainerHasTitle(trainer *MainServer.Trainer, titleName string) bool {
	if trainer.Decoration == nil || trainer.Decoration.Titles == nil {
		return false
	}

	// 检查训练师的称号列表
	for _, title := range trainer.Decoration.Titles {
		if title.Type.String() == titleName {
			return true
		}
	}
	return false
}

// checkTimeCondition 检查时间条件
func checkTimeCondition(jsonValue string) bool {
	// 解析时间条件的JSON配置
	// 这里可以根据具体需求实现复杂的时间逻辑
	// 例如：特定时间段、星期几、节假日等
	if jsonValue == "" {
		return true
	}

	// 简单的时间检查示例
	now := time.Now()
	hour := now.Hour()

	// 示例：只在白天（6-18点）可以接受任务
	if jsonValue == "daytime" {
		return hour >= 6 && hour <= 18
	}

	// 示例：只在夜晚（19-5点）可以接受任务
	if jsonValue == "nighttime" {
		return hour >= 19 || hour <= 5
	}

	return true // 默认通过
}

// checkCustomCondition 检查自定义条件
func checkCustomCondition(jsonValue string, progress map[string]interface{}) bool {
	// 解析自定义条件的JSON配置
	// 这里可以根据具体需求实现复杂的自定义逻辑
	if jsonValue == "" {
		return true
	}

	// 示例：检查多个条件的组合
	// 可以解析JSON来实现复杂的逻辑判断
	return true // 占位实现
}

// getProgressCount 从进度数据中获取计数
func getProgressCount(progress map[string]interface{}, key string) int32 {
	if progress == nil {
		return 0
	}

	value, exists := progress[key]
	if !exists {
		return 0
	}

	switch v := value.(type) {
	case int:
		return int32(v)
	case int32:
		return v
	case int64:
		return int32(v)
	case float64:
		return int32(v)
	default:
		return 0
	}
}
