// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeTeam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Pokes         []*Poke                `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"`
	CreateTs      int64                  `protobuf:"varint,5,opt,name=createTs,proto3" json:"createTs,omitempty"`
	UpdateTs      int64                  `protobuf:"varint,6,opt,name=updateTs,proto3" json:"updateTs,omitempty"`
	HireTids      []int64                `protobuf:"varint,7,rep,packed,name=hireTids,proto3" json:"hireTids,omitempty"` //租赁人
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeam) Reset() {
	*x = PokeTeam{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeam) ProtoMessage() {}

func (x *PokeTeam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeam.ProtoReflect.Descriptor instead.
func (*PokeTeam) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{0}
}

func (x *PokeTeam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PokeTeam) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeTeam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeTeam) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeTeam) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PokeTeam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PokeTeam) GetHireTids() []int64 {
	if x != nil {
		return x.HireTids
	}
	return nil
}

var File_MainServer_PokeTeam_proto protoreflect.FileDescriptor

const file_MainServer_PokeTeam_proto_rawDesc = "" +
	"\n" +
	"\x19MainServer/PokeTeam.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\"\xbc\x01\n" +
	"\bPokeTeam\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12&\n" +
	"\x05pokes\x18\x04 \x03(\v2\x10.MainServer.PokeR\x05pokes\x12\x1a\n" +
	"\bcreateTs\x18\x05 \x01(\x03R\bcreateTs\x12\x1a\n" +
	"\bupdateTs\x18\x06 \x01(\x03R\bupdateTs\x12\x1a\n" +
	"\bhireTids\x18\a \x03(\x03R\bhireTidsB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeTeam_proto_rawDescOnce sync.Once
	file_MainServer_PokeTeam_proto_rawDescData []byte
)

func file_MainServer_PokeTeam_proto_rawDescGZIP() []byte {
	file_MainServer_PokeTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeTeam_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeTeam_proto_rawDesc), len(file_MainServer_PokeTeam_proto_rawDesc)))
	})
	return file_MainServer_PokeTeam_proto_rawDescData
}

var file_MainServer_PokeTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_PokeTeam_proto_goTypes = []any{
	(*PokeTeam)(nil), // 0: MainServer.PokeTeam
	(*Poke)(nil),     // 1: MainServer.Poke
}
var file_MainServer_PokeTeam_proto_depIdxs = []int32{
	1, // 0: MainServer.PokeTeam.pokes:type_name -> MainServer.Poke
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_MainServer_PokeTeam_proto_init() }
func file_MainServer_PokeTeam_proto_init() {
	if File_MainServer_PokeTeam_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeTeam_proto_rawDesc), len(file_MainServer_PokeTeam_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeTeam_proto_depIdxs,
		MessageInfos:      file_MainServer_PokeTeam_proto_msgTypes,
	}.Build()
	File_MainServer_PokeTeam_proto = out.File
	file_MainServer_PokeTeam_proto_goTypes = nil
	file_MainServer_PokeTeam_proto_depIdxs = nil
}
