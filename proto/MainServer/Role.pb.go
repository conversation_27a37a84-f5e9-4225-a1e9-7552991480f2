// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Role.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RoleDutyType int32

const (
	RoleDutyType_role_normal  RoleDutyType = 0
	RoleDutyType_role_battle  RoleDutyType = 1
	RoleDutyType_role_healing RoleDutyType = 2
	RoleDutyType_role_store   RoleDutyType = 3
	RoleDutyType_role_train   RoleDutyType = 4 //列车
	RoleDutyType_role_ship    RoleDutyType = 5 //轮船
)

// Enum value maps for RoleDutyType.
var (
	RoleDutyType_name = map[int32]string{
		0: "role_normal",
		1: "role_battle",
		2: "role_healing",
		3: "role_store",
		4: "role_train",
		5: "role_ship",
	}
	RoleDutyType_value = map[string]int32{
		"role_normal":  0,
		"role_battle":  1,
		"role_healing": 2,
		"role_store":   3,
		"role_train":   4,
		"role_ship":    5,
	}
)

func (x RoleDutyType) Enum() *RoleDutyType {
	p := new(RoleDutyType)
	*p = x
	return p
}

func (x RoleDutyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleDutyType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Role_proto_enumTypes[0].Descriptor()
}

func (RoleDutyType) Type() protoreflect.EnumType {
	return &file_MainServer_Role_proto_enumTypes[0]
}

func (x RoleDutyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleDutyType.Descriptor instead.
func (RoleDutyType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{0}
}

type NpcRoleConfig struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Name                  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Duty                  RoleDutyType           `protobuf:"varint,2,opt,name=duty,proto3,enum=MainServer.RoleDutyType" json:"duty,omitempty"`
	Cloth                 *TrainerCloth          `protobuf:"bytes,3,opt,name=cloth,proto3" json:"cloth,omitempty"`
	FollowPoke            *TrainerFollowPoke     `protobuf:"bytes,4,opt,name=follow_poke,json=followPoke,proto3" json:"follow_poke,omitempty"` //跟随的宝可梦
	Gender                Gender                 `protobuf:"varint,5,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`
	YarnName              string                 `protobuf:"bytes,6,opt,name=yarn_name,json=yarnName,proto3" json:"yarn_name,omitempty"`
	YarnTitle             string                 `protobuf:"bytes,7,opt,name=yarn_title,json=yarnTitle,proto3" json:"yarn_title,omitempty"`
	Team                  TrainerTeam            `protobuf:"varint,8,opt,name=team,proto3,enum=MainServer.TrainerTeam" json:"team,omitempty"`
	DefaultTransferPoints []string               `protobuf:"bytes,9,rep,name=default_transfer_points,json=defaultTransferPoints,proto3" json:"default_transfer_points,omitempty"` //默认传送点
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *NpcRoleConfig) Reset() {
	*x = NpcRoleConfig{}
	mi := &file_MainServer_Role_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NpcRoleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NpcRoleConfig) ProtoMessage() {}

func (x *NpcRoleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Role_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NpcRoleConfig.ProtoReflect.Descriptor instead.
func (*NpcRoleConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{0}
}

func (x *NpcRoleConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NpcRoleConfig) GetDuty() RoleDutyType {
	if x != nil {
		return x.Duty
	}
	return RoleDutyType_role_normal
}

func (x *NpcRoleConfig) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

func (x *NpcRoleConfig) GetFollowPoke() *TrainerFollowPoke {
	if x != nil {
		return x.FollowPoke
	}
	return nil
}

func (x *NpcRoleConfig) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

func (x *NpcRoleConfig) GetYarnName() string {
	if x != nil {
		return x.YarnName
	}
	return ""
}

func (x *NpcRoleConfig) GetYarnTitle() string {
	if x != nil {
		return x.YarnTitle
	}
	return ""
}

func (x *NpcRoleConfig) GetTeam() TrainerTeam {
	if x != nil {
		return x.Team
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *NpcRoleConfig) GetDefaultTransferPoints() []string {
	if x != nil {
		return x.DefaultTransferPoints
	}
	return nil
}

var File_MainServer_Role_proto protoreflect.FileDescriptor

const file_MainServer_Role_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Role.proto\x12\n" +
	"MainServer\x1a\x18MainServer/Trainer.proto\x1a\x1dMainServer/TrainerCloth.proto\x1a\x1cMainServer/TrainerTeam.proto\x1a\x15MainServer/Poke.proto\"\x8e\x03\n" +
	"\rNpcRoleConfig\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12,\n" +
	"\x04duty\x18\x02 \x01(\x0e2\x18.MainServer.RoleDutyTypeR\x04duty\x12.\n" +
	"\x05cloth\x18\x03 \x01(\v2\x18.MainServer.TrainerClothR\x05cloth\x12>\n" +
	"\vfollow_poke\x18\x04 \x01(\v2\x1d.MainServer.TrainerFollowPokeR\n" +
	"followPoke\x12*\n" +
	"\x06gender\x18\x05 \x01(\x0e2\x12.MainServer.GenderR\x06gender\x12\x1b\n" +
	"\tyarn_name\x18\x06 \x01(\tR\byarnName\x12\x1d\n" +
	"\n" +
	"yarn_title\x18\a \x01(\tR\tyarnTitle\x12+\n" +
	"\x04team\x18\b \x01(\x0e2\x17.MainServer.TrainerTeamR\x04team\x126\n" +
	"\x17default_transfer_points\x18\t \x03(\tR\x15defaultTransferPoints*q\n" +
	"\fRoleDutyType\x12\x0f\n" +
	"\vrole_normal\x10\x00\x12\x0f\n" +
	"\vrole_battle\x10\x01\x12\x10\n" +
	"\frole_healing\x10\x02\x12\x0e\n" +
	"\n" +
	"role_store\x10\x03\x12\x0e\n" +
	"\n" +
	"role_train\x10\x04\x12\r\n" +
	"\trole_ship\x10\x05B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Role_proto_rawDescOnce sync.Once
	file_MainServer_Role_proto_rawDescData []byte
)

func file_MainServer_Role_proto_rawDescGZIP() []byte {
	file_MainServer_Role_proto_rawDescOnce.Do(func() {
		file_MainServer_Role_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Role_proto_rawDesc), len(file_MainServer_Role_proto_rawDesc)))
	})
	return file_MainServer_Role_proto_rawDescData
}

var file_MainServer_Role_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Role_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_Role_proto_goTypes = []any{
	(RoleDutyType)(0),         // 0: MainServer.RoleDutyType
	(*NpcRoleConfig)(nil),     // 1: MainServer.NpcRoleConfig
	(*TrainerCloth)(nil),      // 2: MainServer.TrainerCloth
	(*TrainerFollowPoke)(nil), // 3: MainServer.TrainerFollowPoke
	(Gender)(0),               // 4: MainServer.Gender
	(TrainerTeam)(0),          // 5: MainServer.TrainerTeam
}
var file_MainServer_Role_proto_depIdxs = []int32{
	0, // 0: MainServer.NpcRoleConfig.duty:type_name -> MainServer.RoleDutyType
	2, // 1: MainServer.NpcRoleConfig.cloth:type_name -> MainServer.TrainerCloth
	3, // 2: MainServer.NpcRoleConfig.follow_poke:type_name -> MainServer.TrainerFollowPoke
	4, // 3: MainServer.NpcRoleConfig.gender:type_name -> MainServer.Gender
	5, // 4: MainServer.NpcRoleConfig.team:type_name -> MainServer.TrainerTeam
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_MainServer_Role_proto_init() }
func file_MainServer_Role_proto_init() {
	if File_MainServer_Role_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	file_MainServer_TrainerTeam_proto_init()
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Role_proto_rawDesc), len(file_MainServer_Role_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Role_proto_goTypes,
		DependencyIndexes: file_MainServer_Role_proto_depIdxs,
		EnumInfos:         file_MainServer_Role_proto_enumTypes,
		MessageInfos:      file_MainServer_Role_proto_msgTypes,
	}.Build()
	File_MainServer_Role_proto = out.File
	file_MainServer_Role_proto_goTypes = nil
	file_MainServer_Role_proto_depIdxs = nil
}
