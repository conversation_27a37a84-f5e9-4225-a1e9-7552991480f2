// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/LocInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MainLandType int32

const (
	MainLandType_MainLand_None      MainLandType = 0
	MainLandType_MainLand_HeartGold MainLandType = 1
	MainLandType_MainLand_Platinum  MainLandType = 2
	MainLandType_MainLand_White     MainLandType = 3
)

// Enum value maps for MainLandType.
var (
	MainLandType_name = map[int32]string{
		0: "MainLand_None",
		1: "MainLand_HeartGold",
		2: "MainLand_Platinum",
		3: "MainLand_White",
	}
	MainLandType_value = map[string]int32{
		"MainLand_None":      0,
		"MainLand_HeartGold": 1,
		"MainLand_Platinum":  2,
		"MainLand_White":     3,
	}
)

func (x MainLandType) Enum() *MainLandType {
	p := new(MainLandType)
	*p = x
	return p
}

func (x MainLandType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MainLandType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[0].Descriptor()
}

func (MainLandType) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[0]
}

func (x MainLandType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MainLandType.Descriptor instead.
func (MainLandType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{0}
}

type InstanceMapTpye int32

const (
	InstanceMapTpye_MapTpye_None         InstanceMapTpye = 0
	InstanceMapTpye_MapTpye_MewtwoAndMew InstanceMapTpye = 1 //超梦与梦幻
)

// Enum value maps for InstanceMapTpye.
var (
	InstanceMapTpye_name = map[int32]string{
		0: "MapTpye_None",
		1: "MapTpye_MewtwoAndMew",
	}
	InstanceMapTpye_value = map[string]int32{
		"MapTpye_None":         0,
		"MapTpye_MewtwoAndMew": 1,
	}
)

func (x InstanceMapTpye) Enum() *InstanceMapTpye {
	p := new(InstanceMapTpye)
	*p = x
	return p
}

func (x InstanceMapTpye) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InstanceMapTpye) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[1].Descriptor()
}

func (InstanceMapTpye) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[1]
}

func (x InstanceMapTpye) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InstanceMapTpye.Descriptor instead.
func (InstanceMapTpye) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{1}
}

type EncounterMethod int32

const (
	// 默认未知方式
	EncounterMethod_ENCOUNTER_METHOD_UNSPECIFIED EncounterMethod = 0
	// 冲浪（在水面上）
	EncounterMethod_SURF EncounterMethod = 1
	// 超级钓竿
	EncounterMethod_SUPER_ROD EncounterMethod = 2
	// 老旧钓竿
	EncounterMethod_OLD_ROD EncounterMethod = 3
	// 好钓竿
	EncounterMethod_GOOD_ROD EncounterMethod = 4
	// 赠送蛋
	EncounterMethod_GIFT_EGG EncounterMethod = 5
	// 普通走路遇敌（草地或洞穴中）
	EncounterMethod_WALK EncounterMethod = 6
	// 仅限一次的特殊遭遇（如传说宝可梦）
	EncounterMethod_ONLY_ONE EncounterMethod = 7
	// NPC 直接赠送
	EncounterMethod_GIFT EncounterMethod = 8
	// 使用碎石技能撞碎岩石
	EncounterMethod_ROCK_SMASH EncounterMethod = 9
	// 使用宝可梦之笛触发的遭遇
	EncounterMethod_POKEFLUTE EncounterMethod = 10
	// 使用喷水壶唤醒树木状的宝可梦
	EncounterMethod_SQUIRT_BOTTLE EncounterMethod = 11
	// 超级钓竿的特定钓鱼点
	EncounterMethod_SUPER_ROD_SPOTS EncounterMethod = 12
	// 冲浪的特定水域
	EncounterMethod_SURF_SPOTS EncounterMethod = 13
	// 黑草丛（可遇到双战/更强敌）
	EncounterMethod_DARK_GRASS EncounterMethod = 14
	// 草丛闪光点
	EncounterMethod_GRASS_SPOTS EncounterMethod = 15
	// 洞穴中罕见的遭遇点
	EncounterMethod_CAVE_SPOTS EncounterMethod = 16
	// 桥上的遭遇点（如天空之桥）
	EncounterMethod_BRIDGE_SPOTS EncounterMethod = 17
	// 使用探测镜（Devon Scope）后可见的宝可梦
	EncounterMethod_DEVON_SCOPE EncounterMethod = 18
	// 黄色花丛中的遭遇
	EncounterMethod_YELLOW_FLOWERS EncounterMethod = 19
	// 红色花丛中的遭遇
	EncounterMethod_RED_FLOWERS EncounterMethod = 20
	// 紫色花丛中的遭遇
	EncounterMethod_PURPLE_FLOWERS EncounterMethod = 21
	// 粗糙地形（岩石区等）
	EncounterMethod_ROUGH_TERRAIN EncounterMethod = 22
	// SOS 呼救战（宝可梦呼叫援军）
	EncounterMethod_SOS_ENCOUNTER EncounterMethod = 23
	// 岛屿扫描功能触发的遭遇
	EncounterMethod_ISLAND_SCAN EncounterMethod = 24
	// 冒泡水域（特殊钓鱼点）
	EncounterMethod_BUBBLING_SPOTS EncounterMethod = 25
	// 树果堆附近的遭遇
	EncounterMethod_BERRY_PILES EncounterMethod = 26
	// 与 NPC 交换获得
	EncounterMethod_NPC_TRADE EncounterMethod = 27
	// 从冒泡点中触发的 SOS 遭遇
	EncounterMethod_SOS_FROM_BUBBLING_SPOT EncounterMethod = 28
	// 草地中游走宝可梦的遭遇
	EncounterMethod_ROAMING_GRASS EncounterMethod = 29
	// 水面中游走宝可梦的遭遇
	EncounterMethod_ROAMING_WATER EncounterMethod = 30
)

// Enum value maps for EncounterMethod.
var (
	EncounterMethod_name = map[int32]string{
		0:  "ENCOUNTER_METHOD_UNSPECIFIED",
		1:  "SURF",
		2:  "SUPER_ROD",
		3:  "OLD_ROD",
		4:  "GOOD_ROD",
		5:  "GIFT_EGG",
		6:  "WALK",
		7:  "ONLY_ONE",
		8:  "GIFT",
		9:  "ROCK_SMASH",
		10: "POKEFLUTE",
		11: "SQUIRT_BOTTLE",
		12: "SUPER_ROD_SPOTS",
		13: "SURF_SPOTS",
		14: "DARK_GRASS",
		15: "GRASS_SPOTS",
		16: "CAVE_SPOTS",
		17: "BRIDGE_SPOTS",
		18: "DEVON_SCOPE",
		19: "YELLOW_FLOWERS",
		20: "RED_FLOWERS",
		21: "PURPLE_FLOWERS",
		22: "ROUGH_TERRAIN",
		23: "SOS_ENCOUNTER",
		24: "ISLAND_SCAN",
		25: "BUBBLING_SPOTS",
		26: "BERRY_PILES",
		27: "NPC_TRADE",
		28: "SOS_FROM_BUBBLING_SPOT",
		29: "ROAMING_GRASS",
		30: "ROAMING_WATER",
	}
	EncounterMethod_value = map[string]int32{
		"ENCOUNTER_METHOD_UNSPECIFIED": 0,
		"SURF":                         1,
		"SUPER_ROD":                    2,
		"OLD_ROD":                      3,
		"GOOD_ROD":                     4,
		"GIFT_EGG":                     5,
		"WALK":                         6,
		"ONLY_ONE":                     7,
		"GIFT":                         8,
		"ROCK_SMASH":                   9,
		"POKEFLUTE":                    10,
		"SQUIRT_BOTTLE":                11,
		"SUPER_ROD_SPOTS":              12,
		"SURF_SPOTS":                   13,
		"DARK_GRASS":                   14,
		"GRASS_SPOTS":                  15,
		"CAVE_SPOTS":                   16,
		"BRIDGE_SPOTS":                 17,
		"DEVON_SCOPE":                  18,
		"YELLOW_FLOWERS":               19,
		"RED_FLOWERS":                  20,
		"PURPLE_FLOWERS":               21,
		"ROUGH_TERRAIN":                22,
		"SOS_ENCOUNTER":                23,
		"ISLAND_SCAN":                  24,
		"BUBBLING_SPOTS":               25,
		"BERRY_PILES":                  26,
		"NPC_TRADE":                    27,
		"SOS_FROM_BUBBLING_SPOT":       28,
		"ROAMING_GRASS":                29,
		"ROAMING_WATER":                30,
	}
)

func (x EncounterMethod) Enum() *EncounterMethod {
	p := new(EncounterMethod)
	*p = x
	return p
}

func (x EncounterMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EncounterMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_LocInfo_proto_enumTypes[2].Descriptor()
}

func (EncounterMethod) Type() protoreflect.EnumType {
	return &file_MainServer_LocInfo_proto_enumTypes[2]
}

func (x EncounterMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EncounterMethod.Descriptor instead.
func (EncounterMethod) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{2}
}

type TrainerLoc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReginId       string                 `protobuf:"bytes,1,opt,name=regin_id,json=reginId,proto3" json:"regin_id,omitempty"`
	X             float32                `protobuf:"fixed32,2,opt,name=x,proto3" json:"x,omitempty"`
	Y             float32                `protobuf:"fixed32,3,opt,name=y,proto3" json:"y,omitempty"`
	Z             float32                `protobuf:"fixed32,4,opt,name=z,proto3" json:"z,omitempty"`
	MainLandType  MainLandType           `protobuf:"varint,5,opt,name=main_land_type,json=mainLandType,proto3,enum=MainServer.MainLandType" json:"main_land_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerLoc) Reset() {
	*x = TrainerLoc{}
	mi := &file_MainServer_LocInfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerLoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerLoc) ProtoMessage() {}

func (x *TrainerLoc) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocInfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerLoc.ProtoReflect.Descriptor instead.
func (*TrainerLoc) Descriptor() ([]byte, []int) {
	return file_MainServer_LocInfo_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerLoc) GetReginId() string {
	if x != nil {
		return x.ReginId
	}
	return ""
}

func (x *TrainerLoc) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *TrainerLoc) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *TrainerLoc) GetZ() float32 {
	if x != nil {
		return x.Z
	}
	return 0
}

func (x *TrainerLoc) GetMainLandType() MainLandType {
	if x != nil {
		return x.MainLandType
	}
	return MainLandType_MainLand_None
}

var File_MainServer_LocInfo_proto protoreflect.FileDescriptor

const file_MainServer_LocInfo_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/LocInfo.proto\x12\n" +
	"MainServer\"\x91\x01\n" +
	"\n" +
	"TrainerLoc\x12\x19\n" +
	"\bregin_id\x18\x01 \x01(\tR\areginId\x12\f\n" +
	"\x01x\x18\x02 \x01(\x02R\x01x\x12\f\n" +
	"\x01y\x18\x03 \x01(\x02R\x01y\x12\f\n" +
	"\x01z\x18\x04 \x01(\x02R\x01z\x12>\n" +
	"\x0emain_land_type\x18\x05 \x01(\x0e2\x18.MainServer.MainLandTypeR\fmainLandType*d\n" +
	"\fMainLandType\x12\x11\n" +
	"\rMainLand_None\x10\x00\x12\x16\n" +
	"\x12MainLand_HeartGold\x10\x01\x12\x15\n" +
	"\x11MainLand_Platinum\x10\x02\x12\x12\n" +
	"\x0eMainLand_White\x10\x03*=\n" +
	"\x0fInstanceMapTpye\x12\x10\n" +
	"\fMapTpye_None\x10\x00\x12\x18\n" +
	"\x14MapTpye_MewtwoAndMew\x10\x01*\xa8\x04\n" +
	"\x0fEncounterMethod\x12 \n" +
	"\x1cENCOUNTER_METHOD_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04SURF\x10\x01\x12\r\n" +
	"\tSUPER_ROD\x10\x02\x12\v\n" +
	"\aOLD_ROD\x10\x03\x12\f\n" +
	"\bGOOD_ROD\x10\x04\x12\f\n" +
	"\bGIFT_EGG\x10\x05\x12\b\n" +
	"\x04WALK\x10\x06\x12\f\n" +
	"\bONLY_ONE\x10\a\x12\b\n" +
	"\x04GIFT\x10\b\x12\x0e\n" +
	"\n" +
	"ROCK_SMASH\x10\t\x12\r\n" +
	"\tPOKEFLUTE\x10\n" +
	"\x12\x11\n" +
	"\rSQUIRT_BOTTLE\x10\v\x12\x13\n" +
	"\x0fSUPER_ROD_SPOTS\x10\f\x12\x0e\n" +
	"\n" +
	"SURF_SPOTS\x10\r\x12\x0e\n" +
	"\n" +
	"DARK_GRASS\x10\x0e\x12\x0f\n" +
	"\vGRASS_SPOTS\x10\x0f\x12\x0e\n" +
	"\n" +
	"CAVE_SPOTS\x10\x10\x12\x10\n" +
	"\fBRIDGE_SPOTS\x10\x11\x12\x0f\n" +
	"\vDEVON_SCOPE\x10\x12\x12\x12\n" +
	"\x0eYELLOW_FLOWERS\x10\x13\x12\x0f\n" +
	"\vRED_FLOWERS\x10\x14\x12\x12\n" +
	"\x0ePURPLE_FLOWERS\x10\x15\x12\x11\n" +
	"\rROUGH_TERRAIN\x10\x16\x12\x11\n" +
	"\rSOS_ENCOUNTER\x10\x17\x12\x0f\n" +
	"\vISLAND_SCAN\x10\x18\x12\x12\n" +
	"\x0eBUBBLING_SPOTS\x10\x19\x12\x0f\n" +
	"\vBERRY_PILES\x10\x1a\x12\r\n" +
	"\tNPC_TRADE\x10\x1b\x12\x1a\n" +
	"\x16SOS_FROM_BUBBLING_SPOT\x10\x1c\x12\x11\n" +
	"\rROAMING_GRASS\x10\x1d\x12\x11\n" +
	"\rROAMING_WATER\x10\x1eB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_LocInfo_proto_rawDescOnce sync.Once
	file_MainServer_LocInfo_proto_rawDescData []byte
)

func file_MainServer_LocInfo_proto_rawDescGZIP() []byte {
	file_MainServer_LocInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_LocInfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_LocInfo_proto_rawDesc), len(file_MainServer_LocInfo_proto_rawDesc)))
	})
	return file_MainServer_LocInfo_proto_rawDescData
}

var file_MainServer_LocInfo_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_MainServer_LocInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_LocInfo_proto_goTypes = []any{
	(MainLandType)(0),    // 0: MainServer.MainLandType
	(InstanceMapTpye)(0), // 1: MainServer.InstanceMapTpye
	(EncounterMethod)(0), // 2: MainServer.EncounterMethod
	(*TrainerLoc)(nil),   // 3: MainServer.TrainerLoc
}
var file_MainServer_LocInfo_proto_depIdxs = []int32{
	0, // 0: MainServer.TrainerLoc.main_land_type:type_name -> MainServer.MainLandType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_MainServer_LocInfo_proto_init() }
func file_MainServer_LocInfo_proto_init() {
	if File_MainServer_LocInfo_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_LocInfo_proto_rawDesc), len(file_MainServer_LocInfo_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_LocInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_LocInfo_proto_depIdxs,
		EnumInfos:         file_MainServer_LocInfo_proto_enumTypes,
		MessageInfos:      file_MainServer_LocInfo_proto_msgTypes,
	}.Build()
	File_MainServer_LocInfo_proto = out.File
	file_MainServer_LocInfo_proto_goTypes = nil
	file_MainServer_LocInfo_proto_depIdxs = nil
}
