// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/BattleRecord.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 战斗记录信息
type BattleRecordInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                              // 自增长ID
	BattleId          string                 `protobuf:"bytes,2,opt,name=battle_id,json=battleId,proto3" json:"battle_id,omitempty"`                                   // 战斗ID
	Tid               int64                  `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"`                                                            // 训练师ID
	BattleType        BattleType             `protobuf:"varint,4,opt,name=battle_type,json=battleType,proto3,enum=MainServer.BattleType" json:"battle_type,omitempty"` // 战斗类型
	PokeNames         string                 `protobuf:"bytes,5,opt,name=poke_names,json=pokeNames,proto3" json:"poke_names,omitempty"`                                // 训练师使用的宝可梦名称，逗号分割
	OpponentPokeNames string                 `protobuf:"bytes,6,opt,name=opponent_poke_names,json=opponentPokeNames,proto3" json:"opponent_poke_names,omitempty"`      // 对手宝可梦名称，逗号分割
	IsWinner          bool                   `protobuf:"varint,7,opt,name=is_winner,json=isWinner,proto3" json:"is_winner,omitempty"`                                  // 是否胜利
	OpponentTid       int64                  `protobuf:"varint,8,opt,name=opponent_tid,json=opponentTid,proto3" json:"opponent_tid,omitempty"`                         // 对手训练师ID (NPC/野生为0)
	BattleResult      string                 `protobuf:"bytes,9,opt,name=battle_result,json=battleResult,proto3" json:"battle_result,omitempty"`                       // 战斗结果 "win", "lose", "draw"
	BattleStartTs     int64                  `protobuf:"varint,10,opt,name=battle_start_ts,json=battleStartTs,proto3" json:"battle_start_ts,omitempty"`                // 战斗开始时间戳
	BattleEndTs       int64                  `protobuf:"varint,11,opt,name=battle_end_ts,json=battleEndTs,proto3" json:"battle_end_ts,omitempty"`                      // 战斗结束时间戳
	CreateTs          int64                  `protobuf:"varint,12,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                                 // 创建时间戳
	UpdateTs          int64                  `protobuf:"varint,13,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                 // 更新时间戳
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BattleRecordInfo) Reset() {
	*x = BattleRecordInfo{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleRecordInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleRecordInfo) ProtoMessage() {}

func (x *BattleRecordInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleRecordInfo.ProtoReflect.Descriptor instead.
func (*BattleRecordInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{0}
}

func (x *BattleRecordInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BattleRecordInfo) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleRecordInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleRecordInfo) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleRecordInfo) GetPokeNames() string {
	if x != nil {
		return x.PokeNames
	}
	return ""
}

func (x *BattleRecordInfo) GetOpponentPokeNames() string {
	if x != nil {
		return x.OpponentPokeNames
	}
	return ""
}

func (x *BattleRecordInfo) GetIsWinner() bool {
	if x != nil {
		return x.IsWinner
	}
	return false
}

func (x *BattleRecordInfo) GetOpponentTid() int64 {
	if x != nil {
		return x.OpponentTid
	}
	return 0
}

func (x *BattleRecordInfo) GetBattleResult() string {
	if x != nil {
		return x.BattleResult
	}
	return ""
}

func (x *BattleRecordInfo) GetBattleStartTs() int64 {
	if x != nil {
		return x.BattleStartTs
	}
	return 0
}

func (x *BattleRecordInfo) GetBattleEndTs() int64 {
	if x != nil {
		return x.BattleEndTs
	}
	return 0
}

func (x *BattleRecordInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *BattleRecordInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 战斗统计信息
type BattleStatistics struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Tid              int64                  `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`                                                    // 训练师ID
	OpponentPokeName string                 `protobuf:"bytes,2,opt,name=opponent_poke_name,json=opponentPokeName,proto3" json:"opponent_poke_name,omitempty"` // 对手宝可梦名称
	BattleCount      int32                  `protobuf:"varint,3,opt,name=battle_count,json=battleCount,proto3" json:"battle_count,omitempty"`                 // 战斗次数
	WinCount         int32                  `protobuf:"varint,4,opt,name=win_count,json=winCount,proto3" json:"win_count,omitempty"`                          // 胜利次数
	LoseCount        int32                  `protobuf:"varint,5,opt,name=lose_count,json=loseCount,proto3" json:"lose_count,omitempty"`                       // 失败次数
	WinRate          float64                `protobuf:"fixed64,6,opt,name=win_rate,json=winRate,proto3" json:"win_rate,omitempty"`                            // 胜率
	TimeRangeMs      int64                  `protobuf:"varint,7,opt,name=time_range_ms,json=timeRangeMs,proto3" json:"time_range_ms,omitempty"`               // 时间范围（毫秒）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BattleStatistics) Reset() {
	*x = BattleStatistics{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleStatistics) ProtoMessage() {}

func (x *BattleStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleStatistics.ProtoReflect.Descriptor instead.
func (*BattleStatistics) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{1}
}

func (x *BattleStatistics) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleStatistics) GetOpponentPokeName() string {
	if x != nil {
		return x.OpponentPokeName
	}
	return ""
}

func (x *BattleStatistics) GetBattleCount() int32 {
	if x != nil {
		return x.BattleCount
	}
	return 0
}

func (x *BattleStatistics) GetWinCount() int32 {
	if x != nil {
		return x.WinCount
	}
	return 0
}

func (x *BattleStatistics) GetLoseCount() int32 {
	if x != nil {
		return x.LoseCount
	}
	return 0
}

func (x *BattleStatistics) GetWinRate() float64 {
	if x != nil {
		return x.WinRate
	}
	return 0
}

func (x *BattleStatistics) GetTimeRangeMs() int64 {
	if x != nil {
		return x.TimeRangeMs
	}
	return 0
}

// RPC请求：获取战斗统计
type RpcGetBattleStatisticsRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OpponentPokeName string                 `protobuf:"bytes,1,opt,name=opponent_poke_name,json=opponentPokeName,proto3" json:"opponent_poke_name,omitempty"` // 对手宝可梦名称
	TimeRangeMs      int64                  `protobuf:"varint,2,opt,name=time_range_ms,json=timeRangeMs,proto3" json:"time_range_ms,omitempty"`               // 时间范围（毫秒）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RpcGetBattleStatisticsRequest) Reset() {
	*x = RpcGetBattleStatisticsRequest{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleStatisticsRequest) ProtoMessage() {}

func (x *RpcGetBattleStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleStatisticsRequest.ProtoReflect.Descriptor instead.
func (*RpcGetBattleStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{2}
}

func (x *RpcGetBattleStatisticsRequest) GetOpponentPokeName() string {
	if x != nil {
		return x.OpponentPokeName
	}
	return ""
}

func (x *RpcGetBattleStatisticsRequest) GetTimeRangeMs() int64 {
	if x != nil {
		return x.TimeRangeMs
	}
	return 0
}

// RPC响应：获取战斗统计
type RpcGetBattleStatisticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statistics    *BattleStatistics      `protobuf:"bytes,1,opt,name=statistics,proto3" json:"statistics,omitempty"` // 战斗统计信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetBattleStatisticsResponse) Reset() {
	*x = RpcGetBattleStatisticsResponse{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleStatisticsResponse) ProtoMessage() {}

func (x *RpcGetBattleStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleStatisticsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetBattleStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{3}
}

func (x *RpcGetBattleStatisticsResponse) GetStatistics() *BattleStatistics {
	if x != nil {
		return x.Statistics
	}
	return nil
}

// RPC请求：获取战斗次数
type RpcGetBattleCountRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OpponentPokeName string                 `protobuf:"bytes,1,opt,name=opponent_poke_name,json=opponentPokeName,proto3" json:"opponent_poke_name,omitempty"` // 对手宝可梦名称
	TimeRangeMs      int64                  `protobuf:"varint,2,opt,name=time_range_ms,json=timeRangeMs,proto3" json:"time_range_ms,omitempty"`               // 时间范围（毫秒）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RpcGetBattleCountRequest) Reset() {
	*x = RpcGetBattleCountRequest{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleCountRequest) ProtoMessage() {}

func (x *RpcGetBattleCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleCountRequest.ProtoReflect.Descriptor instead.
func (*RpcGetBattleCountRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{4}
}

func (x *RpcGetBattleCountRequest) GetOpponentPokeName() string {
	if x != nil {
		return x.OpponentPokeName
	}
	return ""
}

func (x *RpcGetBattleCountRequest) GetTimeRangeMs() int64 {
	if x != nil {
		return x.TimeRangeMs
	}
	return 0
}

// RPC响应：获取战斗次数
type RpcGetBattleCountResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Tid              int64                  `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`                                                    // 训练师ID
	OpponentPokeName string                 `protobuf:"bytes,2,opt,name=opponent_poke_name,json=opponentPokeName,proto3" json:"opponent_poke_name,omitempty"` // 对手宝可梦名称
	BattleCount      int32                  `protobuf:"varint,3,opt,name=battle_count,json=battleCount,proto3" json:"battle_count,omitempty"`                 // 战斗次数
	TimeRangeMs      int64                  `protobuf:"varint,4,opt,name=time_range_ms,json=timeRangeMs,proto3" json:"time_range_ms,omitempty"`               // 时间范围（毫秒）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RpcGetBattleCountResponse) Reset() {
	*x = RpcGetBattleCountResponse{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleCountResponse) ProtoMessage() {}

func (x *RpcGetBattleCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleCountResponse.ProtoReflect.Descriptor instead.
func (*RpcGetBattleCountResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{5}
}

func (x *RpcGetBattleCountResponse) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *RpcGetBattleCountResponse) GetOpponentPokeName() string {
	if x != nil {
		return x.OpponentPokeName
	}
	return ""
}

func (x *RpcGetBattleCountResponse) GetBattleCount() int32 {
	if x != nil {
		return x.BattleCount
	}
	return 0
}

func (x *RpcGetBattleCountResponse) GetTimeRangeMs() int64 {
	if x != nil {
		return x.TimeRangeMs
	}
	return 0
}

// RPC请求：获取战斗记录列表
type RpcGetBattleRecordsRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OpponentPokeName string                 `protobuf:"bytes,1,opt,name=opponent_poke_name,json=opponentPokeName,proto3" json:"opponent_poke_name,omitempty"` // 对手宝可梦名称
	TimeRangeMs      int64                  `protobuf:"varint,2,opt,name=time_range_ms,json=timeRangeMs,proto3" json:"time_range_ms,omitempty"`               // 时间范围（毫秒）
	Limit            int32                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`                                                // 限制返回数量
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RpcGetBattleRecordsRequest) Reset() {
	*x = RpcGetBattleRecordsRequest{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleRecordsRequest) ProtoMessage() {}

func (x *RpcGetBattleRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleRecordsRequest.ProtoReflect.Descriptor instead.
func (*RpcGetBattleRecordsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{6}
}

func (x *RpcGetBattleRecordsRequest) GetOpponentPokeName() string {
	if x != nil {
		return x.OpponentPokeName
	}
	return ""
}

func (x *RpcGetBattleRecordsRequest) GetTimeRangeMs() int64 {
	if x != nil {
		return x.TimeRangeMs
	}
	return 0
}

func (x *RpcGetBattleRecordsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// RPC响应：获取战斗记录列表
type RpcGetBattleRecordsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Records       []*BattleRecordInfo    `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"` // 战斗记录列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetBattleRecordsResponse) Reset() {
	*x = RpcGetBattleRecordsResponse{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleRecordsResponse) ProtoMessage() {}

func (x *RpcGetBattleRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleRecordsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetBattleRecordsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{7}
}

func (x *RpcGetBattleRecordsResponse) GetRecords() []*BattleRecordInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

// RPC请求：按战斗类型获取统计
type RpcGetBattleStatisticsByTypeRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OpponentPokeName string                 `protobuf:"bytes,1,opt,name=opponent_poke_name,json=opponentPokeName,proto3" json:"opponent_poke_name,omitempty"`         // 对手宝可梦名称
	BattleType       BattleType             `protobuf:"varint,2,opt,name=battle_type,json=battleType,proto3,enum=MainServer.BattleType" json:"battle_type,omitempty"` // 战斗类型
	TimeRangeMs      int64                  `protobuf:"varint,3,opt,name=time_range_ms,json=timeRangeMs,proto3" json:"time_range_ms,omitempty"`                       // 时间范围（毫秒）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RpcGetBattleStatisticsByTypeRequest) Reset() {
	*x = RpcGetBattleStatisticsByTypeRequest{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleStatisticsByTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleStatisticsByTypeRequest) ProtoMessage() {}

func (x *RpcGetBattleStatisticsByTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleStatisticsByTypeRequest.ProtoReflect.Descriptor instead.
func (*RpcGetBattleStatisticsByTypeRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{8}
}

func (x *RpcGetBattleStatisticsByTypeRequest) GetOpponentPokeName() string {
	if x != nil {
		return x.OpponentPokeName
	}
	return ""
}

func (x *RpcGetBattleStatisticsByTypeRequest) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *RpcGetBattleStatisticsByTypeRequest) GetTimeRangeMs() int64 {
	if x != nil {
		return x.TimeRangeMs
	}
	return 0
}

// RPC响应：按战斗类型获取统计
type RpcGetBattleStatisticsByTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statistics    *BattleStatistics      `protobuf:"bytes,1,opt,name=statistics,proto3" json:"statistics,omitempty"` // 战斗统计信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetBattleStatisticsByTypeResponse) Reset() {
	*x = RpcGetBattleStatisticsByTypeResponse{}
	mi := &file_MainServer_BattleRecord_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetBattleStatisticsByTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetBattleStatisticsByTypeResponse) ProtoMessage() {}

func (x *RpcGetBattleStatisticsByTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleRecord_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetBattleStatisticsByTypeResponse.ProtoReflect.Descriptor instead.
func (*RpcGetBattleStatisticsByTypeResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleRecord_proto_rawDescGZIP(), []int{9}
}

func (x *RpcGetBattleStatisticsByTypeResponse) GetStatistics() *BattleStatistics {
	if x != nil {
		return x.Statistics
	}
	return nil
}

var File_MainServer_BattleRecord_proto protoreflect.FileDescriptor

const file_MainServer_BattleRecord_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/BattleRecord.proto\x12\n" +
	"MainServer\x1a\x1bMainServer/BattleInfo.proto\"\xc4\x03\n" +
	"\x10BattleRecordInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tbattle_id\x18\x02 \x01(\tR\bbattleId\x12\x10\n" +
	"\x03tid\x18\x03 \x01(\x03R\x03tid\x127\n" +
	"\vbattle_type\x18\x04 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\x12\x1d\n" +
	"\n" +
	"poke_names\x18\x05 \x01(\tR\tpokeNames\x12.\n" +
	"\x13opponent_poke_names\x18\x06 \x01(\tR\x11opponentPokeNames\x12\x1b\n" +
	"\tis_winner\x18\a \x01(\bR\bisWinner\x12!\n" +
	"\fopponent_tid\x18\b \x01(\x03R\vopponentTid\x12#\n" +
	"\rbattle_result\x18\t \x01(\tR\fbattleResult\x12&\n" +
	"\x0fbattle_start_ts\x18\n" +
	" \x01(\x03R\rbattleStartTs\x12\"\n" +
	"\rbattle_end_ts\x18\v \x01(\x03R\vbattleEndTs\x12\x1b\n" +
	"\tcreate_ts\x18\f \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\r \x01(\x03R\bupdateTs\"\xf0\x01\n" +
	"\x10BattleStatistics\x12\x10\n" +
	"\x03tid\x18\x01 \x01(\x03R\x03tid\x12,\n" +
	"\x12opponent_poke_name\x18\x02 \x01(\tR\x10opponentPokeName\x12!\n" +
	"\fbattle_count\x18\x03 \x01(\x05R\vbattleCount\x12\x1b\n" +
	"\twin_count\x18\x04 \x01(\x05R\bwinCount\x12\x1d\n" +
	"\n" +
	"lose_count\x18\x05 \x01(\x05R\tloseCount\x12\x19\n" +
	"\bwin_rate\x18\x06 \x01(\x01R\awinRate\x12\"\n" +
	"\rtime_range_ms\x18\a \x01(\x03R\vtimeRangeMs\"q\n" +
	"\x1dRpcGetBattleStatisticsRequest\x12,\n" +
	"\x12opponent_poke_name\x18\x01 \x01(\tR\x10opponentPokeName\x12\"\n" +
	"\rtime_range_ms\x18\x02 \x01(\x03R\vtimeRangeMs\"^\n" +
	"\x1eRpcGetBattleStatisticsResponse\x12<\n" +
	"\n" +
	"statistics\x18\x01 \x01(\v2\x1c.MainServer.BattleStatisticsR\n" +
	"statistics\"l\n" +
	"\x18RpcGetBattleCountRequest\x12,\n" +
	"\x12opponent_poke_name\x18\x01 \x01(\tR\x10opponentPokeName\x12\"\n" +
	"\rtime_range_ms\x18\x02 \x01(\x03R\vtimeRangeMs\"\xa2\x01\n" +
	"\x19RpcGetBattleCountResponse\x12\x10\n" +
	"\x03tid\x18\x01 \x01(\x03R\x03tid\x12,\n" +
	"\x12opponent_poke_name\x18\x02 \x01(\tR\x10opponentPokeName\x12!\n" +
	"\fbattle_count\x18\x03 \x01(\x05R\vbattleCount\x12\"\n" +
	"\rtime_range_ms\x18\x04 \x01(\x03R\vtimeRangeMs\"\x84\x01\n" +
	"\x1aRpcGetBattleRecordsRequest\x12,\n" +
	"\x12opponent_poke_name\x18\x01 \x01(\tR\x10opponentPokeName\x12\"\n" +
	"\rtime_range_ms\x18\x02 \x01(\x03R\vtimeRangeMs\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x05R\x05limit\"U\n" +
	"\x1bRpcGetBattleRecordsResponse\x126\n" +
	"\arecords\x18\x01 \x03(\v2\x1c.MainServer.BattleRecordInfoR\arecords\"\xb0\x01\n" +
	"#RpcGetBattleStatisticsByTypeRequest\x12,\n" +
	"\x12opponent_poke_name\x18\x01 \x01(\tR\x10opponentPokeName\x127\n" +
	"\vbattle_type\x18\x02 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\x12\"\n" +
	"\rtime_range_ms\x18\x03 \x01(\x03R\vtimeRangeMs\"d\n" +
	"$RpcGetBattleStatisticsByTypeResponse\x12<\n" +
	"\n" +
	"statistics\x18\x01 \x01(\v2\x1c.MainServer.BattleStatisticsR\n" +
	"statisticsB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_BattleRecord_proto_rawDescOnce sync.Once
	file_MainServer_BattleRecord_proto_rawDescData []byte
)

func file_MainServer_BattleRecord_proto_rawDescGZIP() []byte {
	file_MainServer_BattleRecord_proto_rawDescOnce.Do(func() {
		file_MainServer_BattleRecord_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_BattleRecord_proto_rawDesc), len(file_MainServer_BattleRecord_proto_rawDesc)))
	})
	return file_MainServer_BattleRecord_proto_rawDescData
}

var file_MainServer_BattleRecord_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_MainServer_BattleRecord_proto_goTypes = []any{
	(*BattleRecordInfo)(nil),                     // 0: MainServer.BattleRecordInfo
	(*BattleStatistics)(nil),                     // 1: MainServer.BattleStatistics
	(*RpcGetBattleStatisticsRequest)(nil),        // 2: MainServer.RpcGetBattleStatisticsRequest
	(*RpcGetBattleStatisticsResponse)(nil),       // 3: MainServer.RpcGetBattleStatisticsResponse
	(*RpcGetBattleCountRequest)(nil),             // 4: MainServer.RpcGetBattleCountRequest
	(*RpcGetBattleCountResponse)(nil),            // 5: MainServer.RpcGetBattleCountResponse
	(*RpcGetBattleRecordsRequest)(nil),           // 6: MainServer.RpcGetBattleRecordsRequest
	(*RpcGetBattleRecordsResponse)(nil),          // 7: MainServer.RpcGetBattleRecordsResponse
	(*RpcGetBattleStatisticsByTypeRequest)(nil),  // 8: MainServer.RpcGetBattleStatisticsByTypeRequest
	(*RpcGetBattleStatisticsByTypeResponse)(nil), // 9: MainServer.RpcGetBattleStatisticsByTypeResponse
	(BattleType)(0),                              // 10: MainServer.BattleType
}
var file_MainServer_BattleRecord_proto_depIdxs = []int32{
	10, // 0: MainServer.BattleRecordInfo.battle_type:type_name -> MainServer.BattleType
	1,  // 1: MainServer.RpcGetBattleStatisticsResponse.statistics:type_name -> MainServer.BattleStatistics
	0,  // 2: MainServer.RpcGetBattleRecordsResponse.records:type_name -> MainServer.BattleRecordInfo
	10, // 3: MainServer.RpcGetBattleStatisticsByTypeRequest.battle_type:type_name -> MainServer.BattleType
	1,  // 4: MainServer.RpcGetBattleStatisticsByTypeResponse.statistics:type_name -> MainServer.BattleStatistics
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_MainServer_BattleRecord_proto_init() }
func file_MainServer_BattleRecord_proto_init() {
	if File_MainServer_BattleRecord_proto != nil {
		return
	}
	file_MainServer_BattleInfo_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_BattleRecord_proto_rawDesc), len(file_MainServer_BattleRecord_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BattleRecord_proto_goTypes,
		DependencyIndexes: file_MainServer_BattleRecord_proto_depIdxs,
		MessageInfos:      file_MainServer_BattleRecord_proto_msgTypes,
	}.Build()
	File_MainServer_BattleRecord_proto = out.File
	file_MainServer_BattleRecord_proto_goTypes = nil
	file_MainServer_BattleRecord_proto_depIdxs = nil
}
