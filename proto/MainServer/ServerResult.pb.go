// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ServerResult.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求
type SinglePokeBoxParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BoxType       PokeBoxType            `protobuf:"varint,1,opt,name=boxType,proto3,enum=MainServer.PokeBoxType" json:"boxType,omitempty"`
	Index         int32                  `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	UpdateTs      int64                  `protobuf:"varint,3,opt,name=updateTs,proto3" json:"updateTs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SinglePokeBoxParam) Reset() {
	*x = SinglePokeBoxParam{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SinglePokeBoxParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SinglePokeBoxParam) ProtoMessage() {}

func (x *SinglePokeBoxParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SinglePokeBoxParam.ProtoReflect.Descriptor instead.
func (*SinglePokeBoxParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{0}
}

func (x *SinglePokeBoxParam) GetBoxType() PokeBoxType {
	if x != nil {
		return x.BoxType
	}
	return PokeBoxType_normal
}

func (x *SinglePokeBoxParam) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SinglePokeBoxParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type FollowPokeParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=pokeId,proto3" json:"pokeId,omitempty"`
	Remove        bool                   `protobuf:"varint,2,opt,name=remove,proto3" json:"remove,omitempty"`
	Ride          bool                   `protobuf:"varint,3,opt,name=ride,proto3" json:"ride,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowPokeParam) Reset() {
	*x = FollowPokeParam{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowPokeParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowPokeParam) ProtoMessage() {}

func (x *FollowPokeParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowPokeParam.ProtoReflect.Descriptor instead.
func (*FollowPokeParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{1}
}

func (x *FollowPokeParam) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *FollowPokeParam) GetRemove() bool {
	if x != nil {
		return x.Remove
	}
	return false
}

func (x *FollowPokeParam) GetRide() bool {
	if x != nil {
		return x.Ride
	}
	return false
}

type InviteBattleParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrainerId     int64                  `protobuf:"varint,1,opt,name=trainerId,proto3" json:"trainerId,omitempty"`
	InviteType    InviteBattleType       `protobuf:"varint,2,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
	BattleType    BattleType             `protobuf:"varint,3,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InviteBattleParam) Reset() {
	*x = InviteBattleParam{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleParam) ProtoMessage() {}

func (x *InviteBattleParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleParam.ProtoReflect.Descriptor instead.
func (*InviteBattleParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{2}
}

func (x *InviteBattleParam) GetTrainerId() int64 {
	if x != nil {
		return x.TrainerId
	}
	return 0
}

func (x *InviteBattleParam) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

func (x *InviteBattleParam) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

type InviteBattleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InviteBattleResponse) Reset() {
	*x = InviteBattleResponse{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleResponse) ProtoMessage() {}

func (x *InviteBattleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleResponse.ProtoReflect.Descriptor instead.
func (*InviteBattleResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{3}
}

func (x *InviteBattleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *InviteBattleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type InviteBattleRecord struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProposerId       int64                  `protobuf:"varint,1,opt,name=proposerId,proto3" json:"proposerId,omitempty"`
	TargetId         int64                  `protobuf:"varint,2,opt,name=targetId,proto3" json:"targetId,omitempty"`
	InviteType       InviteBattleType       `protobuf:"varint,3,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
	InviteTime       int64                  `protobuf:"varint,4,opt,name=inviteTime,proto3" json:"inviteTime,omitempty"`
	Responded        bool                   `protobuf:"varint,5,opt,name=responded,proto3" json:"responded,omitempty"`
	Accepted         bool                   `protobuf:"varint,6,opt,name=accepted,proto3" json:"accepted,omitempty"`
	InviteBattleType BattleType             `protobuf:"varint,7,opt,name=inviteBattleType,proto3,enum=MainServer.BattleType" json:"inviteBattleType,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *InviteBattleRecord) Reset() {
	*x = InviteBattleRecord{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleRecord) ProtoMessage() {}

func (x *InviteBattleRecord) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleRecord.ProtoReflect.Descriptor instead.
func (*InviteBattleRecord) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{4}
}

func (x *InviteBattleRecord) GetProposerId() int64 {
	if x != nil {
		return x.ProposerId
	}
	return 0
}

func (x *InviteBattleRecord) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *InviteBattleRecord) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

func (x *InviteBattleRecord) GetInviteTime() int64 {
	if x != nil {
		return x.InviteTime
	}
	return 0
}

func (x *InviteBattleRecord) GetResponded() bool {
	if x != nil {
		return x.Responded
	}
	return false
}

func (x *InviteBattleRecord) GetAccepted() bool {
	if x != nil {
		return x.Accepted
	}
	return false
}

func (x *InviteBattleRecord) GetInviteBattleType() BattleType {
	if x != nil {
		return x.InviteBattleType
	}
	return BattleType_BattleType_Unknow
}

type InviteBattleAcceptParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProposerId    int64                  `protobuf:"varint,1,opt,name=proposerId,proto3" json:"proposerId,omitempty"`
	Accept        bool                   `protobuf:"varint,2,opt,name=accept,proto3" json:"accept,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InviteBattleAcceptParam) Reset() {
	*x = InviteBattleAcceptParam{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleAcceptParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleAcceptParam) ProtoMessage() {}

func (x *InviteBattleAcceptParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleAcceptParam.ProtoReflect.Descriptor instead.
func (*InviteBattleAcceptParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{5}
}

func (x *InviteBattleAcceptParam) GetProposerId() int64 {
	if x != nil {
		return x.ProposerId
	}
	return 0
}

func (x *InviteBattleAcceptParam) GetAccept() bool {
	if x != nil {
		return x.Accept
	}
	return false
}

type InviteBattleAcceptResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MatchId       string                 `protobuf:"bytes,3,opt,name=matchId,proto3" json:"matchId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InviteBattleAcceptResponse) Reset() {
	*x = InviteBattleAcceptResponse{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleAcceptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleAcceptResponse) ProtoMessage() {}

func (x *InviteBattleAcceptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleAcceptResponse.ProtoReflect.Descriptor instead.
func (*InviteBattleAcceptResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{6}
}

func (x *InviteBattleAcceptResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *InviteBattleAcceptResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *InviteBattleAcceptResponse) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

// 响应
type PokesResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts            int64                  `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Pokes         []*Poke                `protobuf:"bytes,3,rep,name=pokes,proto3" json:"pokes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokesResult) Reset() {
	*x = PokesResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokesResult) ProtoMessage() {}

func (x *PokesResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokesResult.ProtoReflect.Descriptor instead.
func (*PokesResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{7}
}

func (x *PokesResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PokesResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *PokesResult) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

type PokeBoxResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts            int64                  `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Boxs          []*PokeBox             `protobuf:"bytes,3,rep,name=boxs,proto3" json:"boxs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBoxResult) Reset() {
	*x = PokeBoxResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBoxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxResult) ProtoMessage() {}

func (x *PokeBoxResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxResult.ProtoReflect.Descriptor instead.
func (*PokeBoxResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{8}
}

func (x *PokeBoxResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PokeBoxResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *PokeBoxResult) GetBoxs() []*PokeBox {
	if x != nil {
		return x.Boxs
	}
	return nil
}

type SinglePokeBoxResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts            int64                  `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Box           *PokeBox               `protobuf:"bytes,3,opt,name=box,proto3" json:"box,omitempty"`
	Pokes         []*Poke                `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SinglePokeBoxResult) Reset() {
	*x = SinglePokeBoxResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SinglePokeBoxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SinglePokeBoxResult) ProtoMessage() {}

func (x *SinglePokeBoxResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SinglePokeBoxResult.ProtoReflect.Descriptor instead.
func (*SinglePokeBoxResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{9}
}

func (x *SinglePokeBoxResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SinglePokeBoxResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *SinglePokeBoxResult) GetBox() *PokeBox {
	if x != nil {
		return x.Box
	}
	return nil
}

func (x *SinglePokeBoxResult) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

type LoginSuccessResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginSuccessResult) Reset() {
	*x = LoginSuccessResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginSuccessResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginSuccessResult) ProtoMessage() {}

func (x *LoginSuccessResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginSuccessResult.ProtoReflect.Descriptor instead.
func (*LoginSuccessResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{10}
}

type TrainersResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trainers      []*Trainer             `protobuf:"bytes,1,rep,name=trainers,proto3" json:"trainers,omitempty"`
	PartyTrainers []*Trainer             `protobuf:"bytes,2,rep,name=partyTrainers,proto3" json:"partyTrainers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainersResult) Reset() {
	*x = TrainersResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainersResult) ProtoMessage() {}

func (x *TrainersResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainersResult.ProtoReflect.Descriptor instead.
func (*TrainersResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{11}
}

func (x *TrainersResult) GetTrainers() []*Trainer {
	if x != nil {
		return x.Trainers
	}
	return nil
}

func (x *TrainersResult) GetPartyTrainers() []*Trainer {
	if x != nil {
		return x.PartyTrainers
	}
	return nil
}

type TrainerSelectResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trainer       *Trainer               `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
	Config        *GameConfig            `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerSelectResult) Reset() {
	*x = TrainerSelectResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerSelectResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerSelectResult) ProtoMessage() {}

func (x *TrainerSelectResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerSelectResult.ProtoReflect.Descriptor instead.
func (*TrainerSelectResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{12}
}

func (x *TrainerSelectResult) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

func (x *TrainerSelectResult) GetConfig() *GameConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type InventorysResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts            int64                  `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Inventorys    []*Inventory           `protobuf:"bytes,3,rep,name=inventorys,proto3" json:"inventorys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InventorysResult) Reset() {
	*x = InventorysResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InventorysResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventorysResult) ProtoMessage() {}

func (x *InventorysResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventorysResult.ProtoReflect.Descriptor instead.
func (*InventorysResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{13}
}

func (x *InventorysResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *InventorysResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *InventorysResult) GetInventorys() []*Inventory {
	if x != nil {
		return x.Inventorys
	}
	return nil
}

type GameConfig struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	OneUserMaxBoxCount        int32                  `protobuf:"varint,1,opt,name=oneUserMaxBoxCount,proto3" json:"oneUserMaxBoxCount,omitempty"`
	OneUserSpecialMaxBoxCount int32                  `protobuf:"varint,2,opt,name=oneUserSpecialMaxBoxCount,proto3" json:"oneUserSpecialMaxBoxCount,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *GameConfig) Reset() {
	*x = GameConfig{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameConfig) ProtoMessage() {}

func (x *GameConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameConfig.ProtoReflect.Descriptor instead.
func (*GameConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{14}
}

func (x *GameConfig) GetOneUserMaxBoxCount() int32 {
	if x != nil {
		return x.OneUserMaxBoxCount
	}
	return 0
}

func (x *GameConfig) GetOneUserSpecialMaxBoxCount() int32 {
	if x != nil {
		return x.OneUserSpecialMaxBoxCount
	}
	return 0
}

type MatchResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts            int64                  `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	NewMatchId    string                 `protobuf:"bytes,3,opt,name=newMatchId,proto3" json:"newMatchId,omitempty"`
	Matchs        []*BattleMatchInfo     `protobuf:"bytes,4,rep,name=matchs,proto3" json:"matchs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchResult) Reset() {
	*x = MatchResult{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchResult) ProtoMessage() {}

func (x *MatchResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchResult.ProtoReflect.Descriptor instead.
func (*MatchResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{15}
}

func (x *MatchResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *MatchResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *MatchResult) GetNewMatchId() string {
	if x != nil {
		return x.NewMatchId
	}
	return ""
}

func (x *MatchResult) GetMatchs() []*BattleMatchInfo {
	if x != nil {
		return x.Matchs
	}
	return nil
}

// 更新宝可梦物品的请求参数
type RpcUpdatePokeItemRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pid           int64                  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`         // 宝可梦ID
	Destroy       bool                   `protobuf:"varint,2,opt,name=destroy,proto3" json:"destroy,omitempty"` // 是否销毁物品（可选，默认为false）
	Item          string                 `protobuf:"bytes,3,opt,name=item,proto3" json:"item,omitempty"`        // 新物品名称（可选，如果不提供则只移除当前物品）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcUpdatePokeItemRequest) Reset() {
	*x = RpcUpdatePokeItemRequest{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcUpdatePokeItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdatePokeItemRequest) ProtoMessage() {}

func (x *RpcUpdatePokeItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdatePokeItemRequest.ProtoReflect.Descriptor instead.
func (*RpcUpdatePokeItemRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{16}
}

func (x *RpcUpdatePokeItemRequest) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RpcUpdatePokeItemRequest) GetDestroy() bool {
	if x != nil {
		return x.Destroy
	}
	return false
}

func (x *RpcUpdatePokeItemRequest) GetItem() string {
	if x != nil {
		return x.Item
	}
	return ""
}

type RpcSalePokemonRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pid           int64                  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`                                     // 宝可梦ID
	Price         int32                  `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`                                 // 价格
	BoxIndex      int32                  `protobuf:"varint,3,opt,name=boxIndex,proto3" json:"boxIndex,omitempty"`                           // 宝可梦所在宝可梦箱的索引
	BoxType       PokeBoxType            `protobuf:"varint,4,opt,name=boxType,proto3,enum=MainServer.PokeBoxType" json:"boxType,omitempty"` // 宝可梦箱类型
	BoxLoc        int32                  `protobuf:"varint,5,opt,name=boxLoc,proto3" json:"boxLoc,omitempty"`                               // 宝可梦箱位置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcSalePokemonRequest) Reset() {
	*x = RpcSalePokemonRequest{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcSalePokemonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcSalePokemonRequest) ProtoMessage() {}

func (x *RpcSalePokemonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcSalePokemonRequest.ProtoReflect.Descriptor instead.
func (*RpcSalePokemonRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{17}
}

func (x *RpcSalePokemonRequest) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RpcSalePokemonRequest) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *RpcSalePokemonRequest) GetBoxIndex() int32 {
	if x != nil {
		return x.BoxIndex
	}
	return 0
}

func (x *RpcSalePokemonRequest) GetBoxType() PokeBoxType {
	if x != nil {
		return x.BoxType
	}
	return PokeBoxType_normal
}

func (x *RpcSalePokemonRequest) GetBoxLoc() int32 {
	if x != nil {
		return x.BoxLoc
	}
	return 0
}

type RpcSalePokemonResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcSalePokemonResponse) Reset() {
	*x = RpcSalePokemonResponse{}
	mi := &file_MainServer_ServerResult_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcSalePokemonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcSalePokemonResponse) ProtoMessage() {}

func (x *RpcSalePokemonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcSalePokemonResponse.ProtoReflect.Descriptor instead.
func (*RpcSalePokemonResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{18}
}

func (x *RpcSalePokemonResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcSalePokemonResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_MainServer_ServerResult_proto protoreflect.FileDescriptor

const file_MainServer_ServerResult_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/ServerResult.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\x1a\x18MainServer/PokeBox.proto\x1a\x18MainServer/Trainer.proto\x1a\x1aMainServer/Inventory.proto\x1a\x1cMainServer/BattleMatch.proto\x1a#MainServer/ServerNotification.proto\x1a\x1bMainServer/BattleInfo.proto\"y\n" +
	"\x12SinglePokeBoxParam\x121\n" +
	"\aboxType\x18\x01 \x01(\x0e2\x17.MainServer.PokeBoxTypeR\aboxType\x12\x14\n" +
	"\x05index\x18\x02 \x01(\x05R\x05index\x12\x1a\n" +
	"\bupdateTs\x18\x03 \x01(\x03R\bupdateTs\"U\n" +
	"\x0fFollowPokeParam\x12\x16\n" +
	"\x06pokeId\x18\x01 \x01(\x03R\x06pokeId\x12\x16\n" +
	"\x06remove\x18\x02 \x01(\bR\x06remove\x12\x12\n" +
	"\x04ride\x18\x03 \x01(\bR\x04ride\"\xa7\x01\n" +
	"\x11InviteBattleParam\x12\x1c\n" +
	"\ttrainerId\x18\x01 \x01(\x03R\ttrainerId\x12<\n" +
	"\n" +
	"inviteType\x18\x02 \x01(\x0e2\x1c.MainServer.InviteBattleTypeR\n" +
	"inviteType\x126\n" +
	"\n" +
	"battleType\x18\x03 \x01(\x0e2\x16.MainServer.BattleTypeR\n" +
	"battleType\"J\n" +
	"\x14InviteBattleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xac\x02\n" +
	"\x12InviteBattleRecord\x12\x1e\n" +
	"\n" +
	"proposerId\x18\x01 \x01(\x03R\n" +
	"proposerId\x12\x1a\n" +
	"\btargetId\x18\x02 \x01(\x03R\btargetId\x12<\n" +
	"\n" +
	"inviteType\x18\x03 \x01(\x0e2\x1c.MainServer.InviteBattleTypeR\n" +
	"inviteType\x12\x1e\n" +
	"\n" +
	"inviteTime\x18\x04 \x01(\x03R\n" +
	"inviteTime\x12\x1c\n" +
	"\tresponded\x18\x05 \x01(\bR\tresponded\x12\x1a\n" +
	"\baccepted\x18\x06 \x01(\bR\baccepted\x12B\n" +
	"\x10inviteBattleType\x18\a \x01(\x0e2\x16.MainServer.BattleTypeR\x10inviteBattleType\"Q\n" +
	"\x17InviteBattleAcceptParam\x12\x1e\n" +
	"\n" +
	"proposerId\x18\x01 \x01(\x03R\n" +
	"proposerId\x12\x16\n" +
	"\x06accept\x18\x02 \x01(\bR\x06accept\"j\n" +
	"\x1aInviteBattleAcceptResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\amatchId\x18\x03 \x01(\tR\amatchId\"Y\n" +
	"\vPokesResult\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x0e\n" +
	"\x02ts\x18\x02 \x01(\x03R\x02ts\x12&\n" +
	"\x05pokes\x18\x03 \x03(\v2\x10.MainServer.PokeR\x05pokes\"\\\n" +
	"\rPokeBoxResult\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x0e\n" +
	"\x02ts\x18\x02 \x01(\x03R\x02ts\x12'\n" +
	"\x04boxs\x18\x03 \x03(\v2\x13.MainServer.PokeBoxR\x04boxs\"\x88\x01\n" +
	"\x13SinglePokeBoxResult\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x0e\n" +
	"\x02ts\x18\x02 \x01(\x03R\x02ts\x12%\n" +
	"\x03box\x18\x03 \x01(\v2\x13.MainServer.PokeBoxR\x03box\x12&\n" +
	"\x05pokes\x18\x04 \x03(\v2\x10.MainServer.PokeR\x05pokes\"\x14\n" +
	"\x12LoginSuccessResult\"|\n" +
	"\x0eTrainersResult\x12/\n" +
	"\btrainers\x18\x01 \x03(\v2\x13.MainServer.TrainerR\btrainers\x129\n" +
	"\rpartyTrainers\x18\x02 \x03(\v2\x13.MainServer.TrainerR\rpartyTrainers\"t\n" +
	"\x13TrainerSelectResult\x12-\n" +
	"\atrainer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\atrainer\x12.\n" +
	"\x06config\x18\x02 \x01(\v2\x16.MainServer.GameConfigR\x06config\"m\n" +
	"\x10InventorysResult\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x0e\n" +
	"\x02ts\x18\x02 \x01(\x03R\x02ts\x125\n" +
	"\n" +
	"inventorys\x18\x03 \x03(\v2\x15.MainServer.InventoryR\n" +
	"inventorys\"z\n" +
	"\n" +
	"GameConfig\x12.\n" +
	"\x12oneUserMaxBoxCount\x18\x01 \x01(\x05R\x12oneUserMaxBoxCount\x12<\n" +
	"\x19oneUserSpecialMaxBoxCount\x18\x02 \x01(\x05R\x19oneUserSpecialMaxBoxCount\"\x86\x01\n" +
	"\vMatchResult\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x0e\n" +
	"\x02ts\x18\x02 \x01(\x03R\x02ts\x12\x1e\n" +
	"\n" +
	"newMatchId\x18\x03 \x01(\tR\n" +
	"newMatchId\x123\n" +
	"\x06matchs\x18\x04 \x03(\v2\x1b.MainServer.BattleMatchInfoR\x06matchs\"Z\n" +
	"\x18RpcUpdatePokeItemRequest\x12\x10\n" +
	"\x03pid\x18\x01 \x01(\x03R\x03pid\x12\x18\n" +
	"\adestroy\x18\x02 \x01(\bR\adestroy\x12\x12\n" +
	"\x04item\x18\x03 \x01(\tR\x04item\"\xa6\x01\n" +
	"\x15RpcSalePokemonRequest\x12\x10\n" +
	"\x03pid\x18\x01 \x01(\x03R\x03pid\x12\x14\n" +
	"\x05price\x18\x02 \x01(\x05R\x05price\x12\x1a\n" +
	"\bboxIndex\x18\x03 \x01(\x05R\bboxIndex\x121\n" +
	"\aboxType\x18\x04 \x01(\x0e2\x17.MainServer.PokeBoxTypeR\aboxType\x12\x16\n" +
	"\x06boxLoc\x18\x05 \x01(\x05R\x06boxLoc\"L\n" +
	"\x16RpcSalePokemonResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessageB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ServerResult_proto_rawDescOnce sync.Once
	file_MainServer_ServerResult_proto_rawDescData []byte
)

func file_MainServer_ServerResult_proto_rawDescGZIP() []byte {
	file_MainServer_ServerResult_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerResult_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ServerResult_proto_rawDesc), len(file_MainServer_ServerResult_proto_rawDesc)))
	})
	return file_MainServer_ServerResult_proto_rawDescData
}

var file_MainServer_ServerResult_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_MainServer_ServerResult_proto_goTypes = []any{
	(*SinglePokeBoxParam)(nil),         // 0: MainServer.SinglePokeBoxParam
	(*FollowPokeParam)(nil),            // 1: MainServer.FollowPokeParam
	(*InviteBattleParam)(nil),          // 2: MainServer.InviteBattleParam
	(*InviteBattleResponse)(nil),       // 3: MainServer.InviteBattleResponse
	(*InviteBattleRecord)(nil),         // 4: MainServer.InviteBattleRecord
	(*InviteBattleAcceptParam)(nil),    // 5: MainServer.InviteBattleAcceptParam
	(*InviteBattleAcceptResponse)(nil), // 6: MainServer.InviteBattleAcceptResponse
	(*PokesResult)(nil),                // 7: MainServer.PokesResult
	(*PokeBoxResult)(nil),              // 8: MainServer.PokeBoxResult
	(*SinglePokeBoxResult)(nil),        // 9: MainServer.SinglePokeBoxResult
	(*LoginSuccessResult)(nil),         // 10: MainServer.LoginSuccessResult
	(*TrainersResult)(nil),             // 11: MainServer.TrainersResult
	(*TrainerSelectResult)(nil),        // 12: MainServer.TrainerSelectResult
	(*InventorysResult)(nil),           // 13: MainServer.InventorysResult
	(*GameConfig)(nil),                 // 14: MainServer.GameConfig
	(*MatchResult)(nil),                // 15: MainServer.MatchResult
	(*RpcUpdatePokeItemRequest)(nil),   // 16: MainServer.RpcUpdatePokeItemRequest
	(*RpcSalePokemonRequest)(nil),      // 17: MainServer.RpcSalePokemonRequest
	(*RpcSalePokemonResponse)(nil),     // 18: MainServer.RpcSalePokemonResponse
	(PokeBoxType)(0),                   // 19: MainServer.PokeBoxType
	(InviteBattleType)(0),              // 20: MainServer.InviteBattleType
	(BattleType)(0),                    // 21: MainServer.BattleType
	(*Poke)(nil),                       // 22: MainServer.Poke
	(*PokeBox)(nil),                    // 23: MainServer.PokeBox
	(*Trainer)(nil),                    // 24: MainServer.Trainer
	(*Inventory)(nil),                  // 25: MainServer.Inventory
	(*BattleMatchInfo)(nil),            // 26: MainServer.BattleMatchInfo
}
var file_MainServer_ServerResult_proto_depIdxs = []int32{
	19, // 0: MainServer.SinglePokeBoxParam.boxType:type_name -> MainServer.PokeBoxType
	20, // 1: MainServer.InviteBattleParam.inviteType:type_name -> MainServer.InviteBattleType
	21, // 2: MainServer.InviteBattleParam.battleType:type_name -> MainServer.BattleType
	20, // 3: MainServer.InviteBattleRecord.inviteType:type_name -> MainServer.InviteBattleType
	21, // 4: MainServer.InviteBattleRecord.inviteBattleType:type_name -> MainServer.BattleType
	22, // 5: MainServer.PokesResult.pokes:type_name -> MainServer.Poke
	23, // 6: MainServer.PokeBoxResult.boxs:type_name -> MainServer.PokeBox
	23, // 7: MainServer.SinglePokeBoxResult.box:type_name -> MainServer.PokeBox
	22, // 8: MainServer.SinglePokeBoxResult.pokes:type_name -> MainServer.Poke
	24, // 9: MainServer.TrainersResult.trainers:type_name -> MainServer.Trainer
	24, // 10: MainServer.TrainersResult.partyTrainers:type_name -> MainServer.Trainer
	24, // 11: MainServer.TrainerSelectResult.trainer:type_name -> MainServer.Trainer
	14, // 12: MainServer.TrainerSelectResult.config:type_name -> MainServer.GameConfig
	25, // 13: MainServer.InventorysResult.inventorys:type_name -> MainServer.Inventory
	26, // 14: MainServer.MatchResult.matchs:type_name -> MainServer.BattleMatchInfo
	19, // 15: MainServer.RpcSalePokemonRequest.boxType:type_name -> MainServer.PokeBoxType
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_MainServer_ServerResult_proto_init() }
func file_MainServer_ServerResult_proto_init() {
	if File_MainServer_ServerResult_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_PokeBox_proto_init()
	file_MainServer_Trainer_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_BattleMatch_proto_init()
	file_MainServer_ServerNotification_proto_init()
	file_MainServer_BattleInfo_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ServerResult_proto_rawDesc), len(file_MainServer_ServerResult_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerResult_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerResult_proto_depIdxs,
		MessageInfos:      file_MainServer_ServerResult_proto_msgTypes,
	}.Build()
	File_MainServer_ServerResult_proto = out.File
	file_MainServer_ServerResult_proto_goTypes = nil
	file_MainServer_ServerResult_proto_depIdxs = nil
}
