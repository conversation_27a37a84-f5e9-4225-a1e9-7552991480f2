// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerTeam int32

const (
	TrainerTeam_TRAINER_TEAM_NONE     TrainerTeam = 0
	TrainerTeam_TRAINER_TEAM_ALL      TrainerTeam = 1  //全部team
	TrainerTeam_TRAINER_TEAM_Rocket   TrainerTeam = 11 //火箭队
	TrainerTeam_TRAINER_TEAM_Magma    TrainerTeam = 12 //熔岩队
	TrainerTeam_TRAINER_TEAM_Aqua     TrainerTeam = 13 //海洋队
	TrainerTeam_TRAINER_TEAM_Galactic TrainerTeam = 14 //银河队
	TrainerTeam_TRAINER_TEAM_Plasma   TrainerTeam = 15 //等离子队
	TrainerTeam_TRAINER_TEAM_Flare    TrainerTeam = 16 //闪焰队
	TrainerTeam_TRAINER_TEAM_Skull    TrainerTeam = 17 //骷髅队
	TrainerTeam_TRAINER_TEAM_Yell     TrainerTeam = 18 //呐喊队
	TrainerTeam_TRAINER_TEAM_Star     TrainerTeam = 19 //天星队
)

// Enum value maps for TrainerTeam.
var (
	TrainerTeam_name = map[int32]string{
		0:  "TRAINER_TEAM_NONE",
		1:  "TRAINER_TEAM_ALL",
		11: "TRAINER_TEAM_Rocket",
		12: "TRAINER_TEAM_Magma",
		13: "TRAINER_TEAM_Aqua",
		14: "TRAINER_TEAM_Galactic",
		15: "TRAINER_TEAM_Plasma",
		16: "TRAINER_TEAM_Flare",
		17: "TRAINER_TEAM_Skull",
		18: "TRAINER_TEAM_Yell",
		19: "TRAINER_TEAM_Star",
	}
	TrainerTeam_value = map[string]int32{
		"TRAINER_TEAM_NONE":     0,
		"TRAINER_TEAM_ALL":      1,
		"TRAINER_TEAM_Rocket":   11,
		"TRAINER_TEAM_Magma":    12,
		"TRAINER_TEAM_Aqua":     13,
		"TRAINER_TEAM_Galactic": 14,
		"TRAINER_TEAM_Plasma":   15,
		"TRAINER_TEAM_Flare":    16,
		"TRAINER_TEAM_Skull":    17,
		"TRAINER_TEAM_Yell":     18,
		"TRAINER_TEAM_Star":     19,
	}
)

func (x TrainerTeam) Enum() *TrainerTeam {
	p := new(TrainerTeam)
	*p = x
	return p
}

func (x TrainerTeam) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerTeam) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTeam_proto_enumTypes[0].Descriptor()
}

func (TrainerTeam) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTeam_proto_enumTypes[0]
}

func (x TrainerTeam) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerTeam.Descriptor instead.
func (TrainerTeam) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{0}
}

type TrainerOnTeamInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// int64 contribution = 1; //阵营贡献
	// int32 level = 2;
	TeamInfo      map[int32]*TrainerTeamInfoDetail `protobuf:"bytes,1,rep,name=teamInfo,proto3" json:"teamInfo,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerOnTeamInfo) Reset() {
	*x = TrainerOnTeamInfo{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerOnTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerOnTeamInfo) ProtoMessage() {}

func (x *TrainerOnTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerOnTeamInfo.ProtoReflect.Descriptor instead.
func (*TrainerOnTeamInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerOnTeamInfo) GetTeamInfo() map[int32]*TrainerTeamInfoDetail {
	if x != nil {
		return x.TeamInfo
	}
	return nil
}

type TrainerTeamInfoDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeamType      TrainerTeam            `protobuf:"varint,1,opt,name=teamType,proto3,enum=MainServer.TrainerTeam" json:"teamType,omitempty"`
	Contribution  int64                  `protobuf:"varint,2,opt,name=contribution,proto3" json:"contribution,omitempty"` //阵营贡献
	Level         int32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	Exp           int32                  `protobuf:"varint,4,opt,name=exp,proto3" json:"exp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerTeamInfoDetail) Reset() {
	*x = TrainerTeamInfoDetail{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerTeamInfoDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTeamInfoDetail) ProtoMessage() {}

func (x *TrainerTeamInfoDetail) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTeamInfoDetail.ProtoReflect.Descriptor instead.
func (*TrainerTeamInfoDetail) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerTeamInfoDetail) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *TrainerTeamInfoDetail) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *TrainerTeamInfoDetail) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *TrainerTeamInfoDetail) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

type RpcAddTrainerTeamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerTeam            `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerTeam" json:"type,omitempty"` //要加入的组织
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAddTrainerTeamRequest) Reset() {
	*x = RpcAddTrainerTeamRequest{}
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAddTrainerTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerTeamRequest) ProtoMessage() {}

func (x *RpcAddTrainerTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerTeamRequest.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerTeamRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{2}
}

func (x *RpcAddTrainerTeamRequest) GetType() TrainerTeam {
	if x != nil {
		return x.Type
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

var File_MainServer_TrainerTeam_proto protoreflect.FileDescriptor

const file_MainServer_TrainerTeam_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/TrainerTeam.proto\x12\n" +
	"MainServer\"\xbc\x01\n" +
	"\x11TrainerOnTeamInfo\x12G\n" +
	"\bteamInfo\x18\x01 \x03(\v2+.MainServer.TrainerOnTeamInfo.TeamInfoEntryR\bteamInfo\x1a^\n" +
	"\rTeamInfoEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x127\n" +
	"\x05value\x18\x02 \x01(\v2!.MainServer.TrainerTeamInfoDetailR\x05value:\x028\x01\"\x98\x01\n" +
	"\x15TrainerTeamInfoDetail\x123\n" +
	"\bteamType\x18\x01 \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\x12\"\n" +
	"\fcontribution\x18\x02 \x01(\x03R\fcontribution\x12\x14\n" +
	"\x05level\x18\x03 \x01(\x05R\x05level\x12\x10\n" +
	"\x03exp\x18\x04 \x01(\x05R\x03exp\"G\n" +
	"\x18RpcAddTrainerTeamRequest\x12+\n" +
	"\x04type\x18\x01 \x01(\x0e2\x17.MainServer.TrainerTeamR\x04type*\x94\x02\n" +
	"\vTrainerTeam\x12\x15\n" +
	"\x11TRAINER_TEAM_NONE\x10\x00\x12\x14\n" +
	"\x10TRAINER_TEAM_ALL\x10\x01\x12\x17\n" +
	"\x13TRAINER_TEAM_Rocket\x10\v\x12\x16\n" +
	"\x12TRAINER_TEAM_Magma\x10\f\x12\x15\n" +
	"\x11TRAINER_TEAM_Aqua\x10\r\x12\x19\n" +
	"\x15TRAINER_TEAM_Galactic\x10\x0e\x12\x17\n" +
	"\x13TRAINER_TEAM_Plasma\x10\x0f\x12\x16\n" +
	"\x12TRAINER_TEAM_Flare\x10\x10\x12\x16\n" +
	"\x12TRAINER_TEAM_Skull\x10\x11\x12\x15\n" +
	"\x11TRAINER_TEAM_Yell\x10\x12\x12\x15\n" +
	"\x11TRAINER_TEAM_Star\x10\x13B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerTeam_proto_rawDescOnce sync.Once
	file_MainServer_TrainerTeam_proto_rawDescData []byte
)

func file_MainServer_TrainerTeam_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerTeam_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTeam_proto_rawDesc), len(file_MainServer_TrainerTeam_proto_rawDesc)))
	})
	return file_MainServer_TrainerTeam_proto_rawDescData
}

var file_MainServer_TrainerTeam_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_TrainerTeam_proto_goTypes = []any{
	(TrainerTeam)(0),                 // 0: MainServer.TrainerTeam
	(*TrainerOnTeamInfo)(nil),        // 1: MainServer.TrainerOnTeamInfo
	(*TrainerTeamInfoDetail)(nil),    // 2: MainServer.TrainerTeamInfoDetail
	(*RpcAddTrainerTeamRequest)(nil), // 3: MainServer.RpcAddTrainerTeamRequest
	nil,                              // 4: MainServer.TrainerOnTeamInfo.TeamInfoEntry
}
var file_MainServer_TrainerTeam_proto_depIdxs = []int32{
	4, // 0: MainServer.TrainerOnTeamInfo.teamInfo:type_name -> MainServer.TrainerOnTeamInfo.TeamInfoEntry
	0, // 1: MainServer.TrainerTeamInfoDetail.teamType:type_name -> MainServer.TrainerTeam
	0, // 2: MainServer.RpcAddTrainerTeamRequest.type:type_name -> MainServer.TrainerTeam
	2, // 3: MainServer.TrainerOnTeamInfo.TeamInfoEntry.value:type_name -> MainServer.TrainerTeamInfoDetail
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerTeam_proto_init() }
func file_MainServer_TrainerTeam_proto_init() {
	if File_MainServer_TrainerTeam_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTeam_proto_rawDesc), len(file_MainServer_TrainerTeam_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerTeam_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerTeam_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerTeam_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerTeam_proto = out.File
	file_MainServer_TrainerTeam_proto_goTypes = nil
	file_MainServer_TrainerTeam_proto_depIdxs = nil
}
