// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerTitleAbout.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerTitleType int32

const (
	TrainerTitleType_TrainerTitleNone             TrainerTitleType = 0
	TrainerTitleType_TrainerTitleFirstGym         TrainerTitleType = 1  //第一次打败道馆 （新手）降低遇敌概率 -0.1
	TrainerTitleType_TrainerTitleFirstChampion    TrainerTitleType = 2  //第一次打败冠军 （地区冠军）降低遇敌概率 -0.2
	TrainerTitleType_TrainerTitleBreedAndHatch100 TrainerTitleType = 3  //第100次养育poke(生育100只，孵蛋100只) （培育达人） //提高好宝宝概率
	TrainerTitleType_TrainerTitleTeamRocket       TrainerTitleType = 4  //火箭队 //遇到好poke的概率 +0.15
	TrainerTitleType_TrainerTitleTeamMagma        TrainerTitleType = 5  //熔岩队  //遇到好poke的概率 +0.05 孵蛋时间 -0.08
	TrainerTitleType_TrainerTitleTeamAqua         TrainerTitleType = 6  //海洋队 //遇到好poke的概率 +0.05 钓到好道具的概率 +0.1
	TrainerTitleType_TrainerTitleTeamGalactic     TrainerTitleType = 7  //银河队 //获得战斗经验的概率 +0.1
	TrainerTitleType_TrainerTitleTeamPlasma       TrainerTitleType = 8  //等离子队 //父母难产的概率 -0.20  //主张宝可梦在人类手中是种不幸，应该要让其回归自然
	TrainerTitleType_TrainerTitleTeamFlare        TrainerTitleType = 9  //闪焰队 //遇闪概率 +0.1
	TrainerTitleType_TrainerTitleTeamSkull        TrainerTitleType = 10 //骷髅队  //阿罗拉地区专给人们找麻烦的流氓组织 //获得战斗evs的概率 +0.1
	TrainerTitleType_TrainerTitleTeamYell         TrainerTitleType = 11 //呐喊队 //遇到好poke的概率 +0.08 树果成长速度 +0.1
	TrainerTitleType_TrainerTitleTeamStar         TrainerTitleType = 12 //天星队 //遇闪概率 +0.05 树果枯萎速度概率 +0.1
)

// Enum value maps for TrainerTitleType.
var (
	TrainerTitleType_name = map[int32]string{
		0:  "TrainerTitleNone",
		1:  "TrainerTitleFirstGym",
		2:  "TrainerTitleFirstChampion",
		3:  "TrainerTitleBreedAndHatch100",
		4:  "TrainerTitleTeamRocket",
		5:  "TrainerTitleTeamMagma",
		6:  "TrainerTitleTeamAqua",
		7:  "TrainerTitleTeamGalactic",
		8:  "TrainerTitleTeamPlasma",
		9:  "TrainerTitleTeamFlare",
		10: "TrainerTitleTeamSkull",
		11: "TrainerTitleTeamYell",
		12: "TrainerTitleTeamStar",
	}
	TrainerTitleType_value = map[string]int32{
		"TrainerTitleNone":             0,
		"TrainerTitleFirstGym":         1,
		"TrainerTitleFirstChampion":    2,
		"TrainerTitleBreedAndHatch100": 3,
		"TrainerTitleTeamRocket":       4,
		"TrainerTitleTeamMagma":        5,
		"TrainerTitleTeamAqua":         6,
		"TrainerTitleTeamGalactic":     7,
		"TrainerTitleTeamPlasma":       8,
		"TrainerTitleTeamFlare":        9,
		"TrainerTitleTeamSkull":        10,
		"TrainerTitleTeamYell":         11,
		"TrainerTitleTeamStar":         12,
	}
)

func (x TrainerTitleType) Enum() *TrainerTitleType {
	p := new(TrainerTitleType)
	*p = x
	return p
}

func (x TrainerTitleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerTitleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTitleAbout_proto_enumTypes[0].Descriptor()
}

func (TrainerTitleType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTitleAbout_proto_enumTypes[0]
}

func (x TrainerTitleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerTitleType.Descriptor instead.
func (TrainerTitleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{0}
}

type TrainerTitle struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Type           TrainerTitleType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerTitleType" json:"type,omitempty"`
	Effect         *TrainerTitleEffect    `protobuf:"bytes,2,opt,name=effect,proto3" json:"effect,omitempty"`
	Price          float32                `protobuf:"fixed32,3,opt,name=price,proto3" json:"price,omitempty"`                                        //价格
	ExpireDuration int64                  `protobuf:"varint,4,opt,name=expire_duration,json=expireDuration,proto3" json:"expire_duration,omitempty"` //多久过期
	SpecialPrice   float32                `protobuf:"fixed32,5,opt,name=special_price,json=specialPrice,proto3" json:"special_price,omitempty"`      //特殊价格
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TrainerTitle) Reset() {
	*x = TrainerTitle{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerTitle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTitle) ProtoMessage() {}

func (x *TrainerTitle) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTitle.ProtoReflect.Descriptor instead.
func (*TrainerTitle) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerTitle) GetType() TrainerTitleType {
	if x != nil {
		return x.Type
	}
	return TrainerTitleType_TrainerTitleNone
}

func (x *TrainerTitle) GetEffect() *TrainerTitleEffect {
	if x != nil {
		return x.Effect
	}
	return nil
}

func (x *TrainerTitle) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *TrainerTitle) GetExpireDuration() int64 {
	if x != nil {
		return x.ExpireDuration
	}
	return 0
}

func (x *TrainerTitle) GetSpecialPrice() float32 {
	if x != nil {
		return x.SpecialPrice
	}
	return 0
}

type TrainerTitleEffect struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	EncounterEnemy     float32                `protobuf:"fixed32,1,opt,name=encounterEnemy,proto3" json:"encounterEnemy,omitempty"`         //遇敌概率
	EncounterShine     float32                `protobuf:"fixed32,2,opt,name=encounterShine,proto3" json:"encounterShine,omitempty"`         //遇闪概率
	BreedGoodPokebaby  float32                `protobuf:"fixed32,3,opt,name=breedGoodPokebaby,proto3" json:"breedGoodPokebaby,omitempty"`   //生育好宝宝的概率
	BreedDifficultBaby float32                `protobuf:"fixed32,4,opt,name=breedDifficultBaby,proto3" json:"breedDifficultBaby,omitempty"` //父母难产的概率
	BorrowGoodPoke     float32                `protobuf:"fixed32,5,opt,name=borrowGoodPoke,proto3" json:"borrowGoodPoke,omitempty"`         //借到好poke的概率
	FishingGoodItem    float32                `protobuf:"fixed32,6,opt,name=fishingGoodItem,proto3" json:"fishingGoodItem,omitempty"`       //钓鱼钓到好道具（金钱）的概率
	FishingGoodPoke    float32                `protobuf:"fixed32,7,opt,name=fishingGoodPoke,proto3" json:"fishingGoodPoke,omitempty"`       //钓鱼钓到好poke的概率
	EncounterGoodEnemy float32                `protobuf:"fixed32,8,opt,name=encounterGoodEnemy,proto3" json:"encounterGoodEnemy,omitempty"` //遇好poke的概率
	GetBattlePoke      float32                `protobuf:"fixed32,9,opt,name=getBattlePoke,proto3" json:"getBattlePoke,omitempty"`           //获得poke的概率
	GetBattleItem      float32                `protobuf:"fixed32,10,opt,name=getBattleItem,proto3" json:"getBattleItem,omitempty"`          //获得道具的概率
	TreeFruitGrowup    float32                `protobuf:"fixed32,11,opt,name=treeFruitGrowup,proto3" json:"treeFruitGrowup,omitempty"`      //树果成长速度
	TreeFruitWithered  float32                `protobuf:"fixed32,12,opt,name=treeFruitWithered,proto3" json:"treeFruitWithered,omitempty"`  //树果枯萎速度概率
	HatchEggTime       float32                `protobuf:"fixed32,13,opt,name=hatchEggTime,proto3" json:"hatchEggTime,omitempty"`            //孵蛋时间
	FishingBattleItem  float32                `protobuf:"fixed32,14,opt,name=fishingBattleItem,proto3" json:"fishingBattleItem,omitempty"`  //钓到战斗道具的概率
	GetBattleExp       float32                `protobuf:"fixed32,15,opt,name=getBattleExp,proto3" json:"getBattleExp,omitempty"`            //获得战斗经验的概率
	GetBattleMoney     float32                `protobuf:"fixed32,16,opt,name=getBattleMoney,proto3" json:"getBattleMoney,omitempty"`        //获得战斗金钱的概率
	GetBattleEvs       float32                `protobuf:"fixed32,17,opt,name=getBattleEvs,proto3" json:"getBattleEvs,omitempty"`            //获得战斗evs的概率
	BreedShine         float32                `protobuf:"fixed32,18,opt,name=breedShine,proto3" json:"breedShine,omitempty"`                //孵蛋遇闪概率
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TrainerTitleEffect) Reset() {
	*x = TrainerTitleEffect{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerTitleEffect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTitleEffect) ProtoMessage() {}

func (x *TrainerTitleEffect) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTitleEffect.ProtoReflect.Descriptor instead.
func (*TrainerTitleEffect) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerTitleEffect) GetEncounterEnemy() float32 {
	if x != nil {
		return x.EncounterEnemy
	}
	return 0
}

func (x *TrainerTitleEffect) GetEncounterShine() float32 {
	if x != nil {
		return x.EncounterShine
	}
	return 0
}

func (x *TrainerTitleEffect) GetBreedGoodPokebaby() float32 {
	if x != nil {
		return x.BreedGoodPokebaby
	}
	return 0
}

func (x *TrainerTitleEffect) GetBreedDifficultBaby() float32 {
	if x != nil {
		return x.BreedDifficultBaby
	}
	return 0
}

func (x *TrainerTitleEffect) GetBorrowGoodPoke() float32 {
	if x != nil {
		return x.BorrowGoodPoke
	}
	return 0
}

func (x *TrainerTitleEffect) GetFishingGoodItem() float32 {
	if x != nil {
		return x.FishingGoodItem
	}
	return 0
}

func (x *TrainerTitleEffect) GetFishingGoodPoke() float32 {
	if x != nil {
		return x.FishingGoodPoke
	}
	return 0
}

func (x *TrainerTitleEffect) GetEncounterGoodEnemy() float32 {
	if x != nil {
		return x.EncounterGoodEnemy
	}
	return 0
}

func (x *TrainerTitleEffect) GetGetBattlePoke() float32 {
	if x != nil {
		return x.GetBattlePoke
	}
	return 0
}

func (x *TrainerTitleEffect) GetGetBattleItem() float32 {
	if x != nil {
		return x.GetBattleItem
	}
	return 0
}

func (x *TrainerTitleEffect) GetTreeFruitGrowup() float32 {
	if x != nil {
		return x.TreeFruitGrowup
	}
	return 0
}

func (x *TrainerTitleEffect) GetTreeFruitWithered() float32 {
	if x != nil {
		return x.TreeFruitWithered
	}
	return 0
}

func (x *TrainerTitleEffect) GetHatchEggTime() float32 {
	if x != nil {
		return x.HatchEggTime
	}
	return 0
}

func (x *TrainerTitleEffect) GetFishingBattleItem() float32 {
	if x != nil {
		return x.FishingBattleItem
	}
	return 0
}

func (x *TrainerTitleEffect) GetGetBattleExp() float32 {
	if x != nil {
		return x.GetBattleExp
	}
	return 0
}

func (x *TrainerTitleEffect) GetGetBattleMoney() float32 {
	if x != nil {
		return x.GetBattleMoney
	}
	return 0
}

func (x *TrainerTitleEffect) GetGetBattleEvs() float32 {
	if x != nil {
		return x.GetBattleEvs
	}
	return 0
}

func (x *TrainerTitleEffect) GetBreedShine() float32 {
	if x != nil {
		return x.BreedShine
	}
	return 0
}

// 训练师称号信息
type TrainerTitleInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                      // 自增长ID
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                    // 训练师ID
	Type          TrainerTitleType       `protobuf:"varint,3,opt,name=type,proto3,enum=MainServer.TrainerTitleType" json:"type,omitempty"` // 称号类型
	CreateTs      int64                  `protobuf:"varint,4,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`          // 创建时间戳
	UpdateTs      int64                  `protobuf:"varint,5,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`          // 更新时间戳
	ExpireTs      int64                  `protobuf:"varint,6,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`          // 过期时间戳 (0表示永不过期)
	Count         int32                  `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`                                // 可佩戴次数 (负数表示无限次)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerTitleInfo) Reset() {
	*x = TrainerTitleInfo{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerTitleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTitleInfo) ProtoMessage() {}

func (x *TrainerTitleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTitleInfo.ProtoReflect.Descriptor instead.
func (*TrainerTitleInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{2}
}

func (x *TrainerTitleInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerTitleInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *TrainerTitleInfo) GetType() TrainerTitleType {
	if x != nil {
		return x.Type
	}
	return TrainerTitleType_TrainerTitleNone
}

func (x *TrainerTitleInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *TrainerTitleInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *TrainerTitleInfo) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

func (x *TrainerTitleInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// RPC响应：获取所有训练师称号
type RpcGetAllTrainerTitlesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Titles        []*TrainerTitleInfo    `protobuf:"bytes,1,rep,name=titles,proto3" json:"titles,omitempty"` // 称号列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetAllTrainerTitlesResponse) Reset() {
	*x = RpcGetAllTrainerTitlesResponse{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetAllTrainerTitlesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAllTrainerTitlesResponse) ProtoMessage() {}

func (x *RpcGetAllTrainerTitlesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAllTrainerTitlesResponse.ProtoReflect.Descriptor instead.
func (*RpcGetAllTrainerTitlesResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{3}
}

func (x *RpcGetAllTrainerTitlesResponse) GetTitles() []*TrainerTitleInfo {
	if x != nil {
		return x.Titles
	}
	return nil
}

// RPC响应：获取不可用训练师称号
type RpcGetUnavailableTrainerTitlesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Titles        []*TrainerTitleInfo    `protobuf:"bytes,1,rep,name=titles,proto3" json:"titles,omitempty"` // 不可用称号列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetUnavailableTrainerTitlesResponse) Reset() {
	*x = RpcGetUnavailableTrainerTitlesResponse{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetUnavailableTrainerTitlesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetUnavailableTrainerTitlesResponse) ProtoMessage() {}

func (x *RpcGetUnavailableTrainerTitlesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetUnavailableTrainerTitlesResponse.ProtoReflect.Descriptor instead.
func (*RpcGetUnavailableTrainerTitlesResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{4}
}

func (x *RpcGetUnavailableTrainerTitlesResponse) GetTitles() []*TrainerTitleInfo {
	if x != nil {
		return x.Titles
	}
	return nil
}

// RPC请求：佩戴训练师称号
type RpcWearTrainerTitleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerTitleType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerTitleType" json:"type,omitempty"` // 称号类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcWearTrainerTitleRequest) Reset() {
	*x = RpcWearTrainerTitleRequest{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcWearTrainerTitleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcWearTrainerTitleRequest) ProtoMessage() {}

func (x *RpcWearTrainerTitleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcWearTrainerTitleRequest.ProtoReflect.Descriptor instead.
func (*RpcWearTrainerTitleRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{5}
}

func (x *RpcWearTrainerTitleRequest) GetType() TrainerTitleType {
	if x != nil {
		return x.Type
	}
	return TrainerTitleType_TrainerTitleNone
}

// RPC响应：佩戴训练师称号
type RpcWearTrainerTitleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcWearTrainerTitleResponse) Reset() {
	*x = RpcWearTrainerTitleResponse{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcWearTrainerTitleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcWearTrainerTitleResponse) ProtoMessage() {}

func (x *RpcWearTrainerTitleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcWearTrainerTitleResponse.ProtoReflect.Descriptor instead.
func (*RpcWearTrainerTitleResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{6}
}

func (x *RpcWearTrainerTitleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcWearTrainerTitleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// RPC请求：取消佩戴训练师称号
type RpcUnwearTrainerTitleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerTitleType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerTitleType" json:"type,omitempty"` // 称号类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcUnwearTrainerTitleRequest) Reset() {
	*x = RpcUnwearTrainerTitleRequest{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcUnwearTrainerTitleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUnwearTrainerTitleRequest) ProtoMessage() {}

func (x *RpcUnwearTrainerTitleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUnwearTrainerTitleRequest.ProtoReflect.Descriptor instead.
func (*RpcUnwearTrainerTitleRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{7}
}

func (x *RpcUnwearTrainerTitleRequest) GetType() TrainerTitleType {
	if x != nil {
		return x.Type
	}
	return TrainerTitleType_TrainerTitleNone
}

// RPC响应：取消佩戴训练师称号
type RpcUnwearTrainerTitleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcUnwearTrainerTitleResponse) Reset() {
	*x = RpcUnwearTrainerTitleResponse{}
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcUnwearTrainerTitleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUnwearTrainerTitleResponse) ProtoMessage() {}

func (x *RpcUnwearTrainerTitleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTitleAbout_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUnwearTrainerTitleResponse.ProtoReflect.Descriptor instead.
func (*RpcUnwearTrainerTitleResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTitleAbout_proto_rawDescGZIP(), []int{8}
}

func (x *RpcUnwearTrainerTitleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcUnwearTrainerTitleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_MainServer_TrainerTitleAbout_proto protoreflect.FileDescriptor

const file_MainServer_TrainerTitleAbout_proto_rawDesc = "" +
	"\n" +
	"\"MainServer/TrainerTitleAbout.proto\x12\n" +
	"MainServer\"\xdc\x01\n" +
	"\fTrainerTitle\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerTitleTypeR\x04type\x126\n" +
	"\x06effect\x18\x02 \x01(\v2\x1e.MainServer.TrainerTitleEffectR\x06effect\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x02R\x05price\x12'\n" +
	"\x0fexpire_duration\x18\x04 \x01(\x03R\x0eexpireDuration\x12#\n" +
	"\rspecial_price\x18\x05 \x01(\x02R\fspecialPrice\"\xf4\x05\n" +
	"\x12TrainerTitleEffect\x12&\n" +
	"\x0eencounterEnemy\x18\x01 \x01(\x02R\x0eencounterEnemy\x12&\n" +
	"\x0eencounterShine\x18\x02 \x01(\x02R\x0eencounterShine\x12,\n" +
	"\x11breedGoodPokebaby\x18\x03 \x01(\x02R\x11breedGoodPokebaby\x12.\n" +
	"\x12breedDifficultBaby\x18\x04 \x01(\x02R\x12breedDifficultBaby\x12&\n" +
	"\x0eborrowGoodPoke\x18\x05 \x01(\x02R\x0eborrowGoodPoke\x12(\n" +
	"\x0ffishingGoodItem\x18\x06 \x01(\x02R\x0ffishingGoodItem\x12(\n" +
	"\x0ffishingGoodPoke\x18\a \x01(\x02R\x0ffishingGoodPoke\x12.\n" +
	"\x12encounterGoodEnemy\x18\b \x01(\x02R\x12encounterGoodEnemy\x12$\n" +
	"\rgetBattlePoke\x18\t \x01(\x02R\rgetBattlePoke\x12$\n" +
	"\rgetBattleItem\x18\n" +
	" \x01(\x02R\rgetBattleItem\x12(\n" +
	"\x0ftreeFruitGrowup\x18\v \x01(\x02R\x0ftreeFruitGrowup\x12,\n" +
	"\x11treeFruitWithered\x18\f \x01(\x02R\x11treeFruitWithered\x12\"\n" +
	"\fhatchEggTime\x18\r \x01(\x02R\fhatchEggTime\x12,\n" +
	"\x11fishingBattleItem\x18\x0e \x01(\x02R\x11fishingBattleItem\x12\"\n" +
	"\fgetBattleExp\x18\x0f \x01(\x02R\fgetBattleExp\x12&\n" +
	"\x0egetBattleMoney\x18\x10 \x01(\x02R\x0egetBattleMoney\x12\"\n" +
	"\fgetBattleEvs\x18\x11 \x01(\x02R\fgetBattleEvs\x12\x1e\n" +
	"\n" +
	"breedShine\x18\x12 \x01(\x02R\n" +
	"breedShine\"\xd3\x01\n" +
	"\x10TrainerTitleInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x120\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1c.MainServer.TrainerTitleTypeR\x04type\x12\x1b\n" +
	"\tcreate_ts\x18\x04 \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x05 \x01(\x03R\bupdateTs\x12\x1b\n" +
	"\texpire_ts\x18\x06 \x01(\x03R\bexpireTs\x12\x14\n" +
	"\x05count\x18\a \x01(\x05R\x05count\"V\n" +
	"\x1eRpcGetAllTrainerTitlesResponse\x124\n" +
	"\x06titles\x18\x01 \x03(\v2\x1c.MainServer.TrainerTitleInfoR\x06titles\"^\n" +
	"&RpcGetUnavailableTrainerTitlesResponse\x124\n" +
	"\x06titles\x18\x01 \x03(\v2\x1c.MainServer.TrainerTitleInfoR\x06titles\"N\n" +
	"\x1aRpcWearTrainerTitleRequest\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerTitleTypeR\x04type\"Q\n" +
	"\x1bRpcWearTrainerTitleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"P\n" +
	"\x1cRpcUnwearTrainerTitleRequest\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerTitleTypeR\x04type\"S\n" +
	"\x1dRpcUnwearTrainerTitleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*\xf8\x02\n" +
	"\x10TrainerTitleType\x12\x14\n" +
	"\x10TrainerTitleNone\x10\x00\x12\x18\n" +
	"\x14TrainerTitleFirstGym\x10\x01\x12\x1d\n" +
	"\x19TrainerTitleFirstChampion\x10\x02\x12 \n" +
	"\x1cTrainerTitleBreedAndHatch100\x10\x03\x12\x1a\n" +
	"\x16TrainerTitleTeamRocket\x10\x04\x12\x19\n" +
	"\x15TrainerTitleTeamMagma\x10\x05\x12\x18\n" +
	"\x14TrainerTitleTeamAqua\x10\x06\x12\x1c\n" +
	"\x18TrainerTitleTeamGalactic\x10\a\x12\x1a\n" +
	"\x16TrainerTitleTeamPlasma\x10\b\x12\x19\n" +
	"\x15TrainerTitleTeamFlare\x10\t\x12\x19\n" +
	"\x15TrainerTitleTeamSkull\x10\n" +
	"\x12\x18\n" +
	"\x14TrainerTitleTeamYell\x10\v\x12\x18\n" +
	"\x14TrainerTitleTeamStar\x10\fB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerTitleAbout_proto_rawDescOnce sync.Once
	file_MainServer_TrainerTitleAbout_proto_rawDescData []byte
)

func file_MainServer_TrainerTitleAbout_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerTitleAbout_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerTitleAbout_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTitleAbout_proto_rawDesc), len(file_MainServer_TrainerTitleAbout_proto_rawDesc)))
	})
	return file_MainServer_TrainerTitleAbout_proto_rawDescData
}

var file_MainServer_TrainerTitleAbout_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerTitleAbout_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_MainServer_TrainerTitleAbout_proto_goTypes = []any{
	(TrainerTitleType)(0),                          // 0: MainServer.TrainerTitleType
	(*TrainerTitle)(nil),                           // 1: MainServer.TrainerTitle
	(*TrainerTitleEffect)(nil),                     // 2: MainServer.TrainerTitleEffect
	(*TrainerTitleInfo)(nil),                       // 3: MainServer.TrainerTitleInfo
	(*RpcGetAllTrainerTitlesResponse)(nil),         // 4: MainServer.RpcGetAllTrainerTitlesResponse
	(*RpcGetUnavailableTrainerTitlesResponse)(nil), // 5: MainServer.RpcGetUnavailableTrainerTitlesResponse
	(*RpcWearTrainerTitleRequest)(nil),             // 6: MainServer.RpcWearTrainerTitleRequest
	(*RpcWearTrainerTitleResponse)(nil),            // 7: MainServer.RpcWearTrainerTitleResponse
	(*RpcUnwearTrainerTitleRequest)(nil),           // 8: MainServer.RpcUnwearTrainerTitleRequest
	(*RpcUnwearTrainerTitleResponse)(nil),          // 9: MainServer.RpcUnwearTrainerTitleResponse
}
var file_MainServer_TrainerTitleAbout_proto_depIdxs = []int32{
	0, // 0: MainServer.TrainerTitle.type:type_name -> MainServer.TrainerTitleType
	2, // 1: MainServer.TrainerTitle.effect:type_name -> MainServer.TrainerTitleEffect
	0, // 2: MainServer.TrainerTitleInfo.type:type_name -> MainServer.TrainerTitleType
	3, // 3: MainServer.RpcGetAllTrainerTitlesResponse.titles:type_name -> MainServer.TrainerTitleInfo
	3, // 4: MainServer.RpcGetUnavailableTrainerTitlesResponse.titles:type_name -> MainServer.TrainerTitleInfo
	0, // 5: MainServer.RpcWearTrainerTitleRequest.type:type_name -> MainServer.TrainerTitleType
	0, // 6: MainServer.RpcUnwearTrainerTitleRequest.type:type_name -> MainServer.TrainerTitleType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerTitleAbout_proto_init() }
func file_MainServer_TrainerTitleAbout_proto_init() {
	if File_MainServer_TrainerTitleAbout_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTitleAbout_proto_rawDesc), len(file_MainServer_TrainerTitleAbout_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerTitleAbout_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerTitleAbout_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerTitleAbout_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerTitleAbout_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerTitleAbout_proto = out.File
	file_MainServer_TrainerTitleAbout_proto_goTypes = nil
	file_MainServer_TrainerTitleAbout_proto_depIdxs = nil
}
