// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Item.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ItemFilterSort int32

const (
	ItemFilterSort_price_i ItemFilterSort = 0 //默认用价格排序
)

// Enum value maps for ItemFilterSort.
var (
	ItemFilterSort_name = map[int32]string{
		0: "price_i",
	}
	ItemFilterSort_value = map[string]int32{
		"price_i": 0,
	}
)

func (x ItemFilterSort) Enum() *ItemFilterSort {
	p := new(ItemFilterSort)
	*p = x
	return p
}

func (x ItemFilterSort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemFilterSort) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Item_proto_enumTypes[0].Descriptor()
}

func (ItemFilterSort) Type() protoreflect.EnumType {
	return &file_MainServer_Item_proto_enumTypes[0]
}

func (x ItemFilterSort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemFilterSort.Descriptor instead.
func (ItemFilterSort) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{0}
}

type Item struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid   int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	// int32 item_type = 3;
	Name          string     `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Count         int32      `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	Price         float64    `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	SaleCount     int32      `protobuf:"varint,7,opt,name=sale_count,json=saleCount,proto3" json:"sale_count,omitempty"`
	Extra         *ItemExtra `protobuf:"bytes,8,opt,name=extra,proto3" json:"extra,omitempty"`                         // 额外信息
	CreateTs      int64      `protobuf:"varint,9,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`  // 创建时间戳
	UpdateTs      int64      `protobuf:"varint,10,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Item) Reset() {
	*x = Item{}
	mi := &file_MainServer_Item_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{0}
}

func (x *Item) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Item) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Item) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Item) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Item) GetSaleCount() int32 {
	if x != nil {
		return x.SaleCount
	}
	return 0
}

func (x *Item) GetExtra() *ItemExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Item) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Item) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type ItemExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItemExtra) Reset() {
	*x = ItemExtra{}
	mi := &file_MainServer_Item_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemExtra) ProtoMessage() {}

func (x *ItemExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemExtra.ProtoReflect.Descriptor instead.
func (*ItemExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{1}
}

// 在前端进行名称筛选 再传到后面
type ItemFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Names         []string               `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	Sort          ItemFilterSort         `protobuf:"varint,2,opt,name=sort,proto3,enum=MainServer.ItemFilterSort" json:"sort,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	MinPrice      float64                `protobuf:"fixed64,5,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice      float64                `protobuf:"fixed64,6,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	UpdateTs      int64                  `protobuf:"varint,7,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Sale          bool                   `protobuf:"varint,8,opt,name=sale,proto3" json:"sale,omitempty"` //sale_count > 0
	Owner         bool                   `protobuf:"varint,13,opt,name=owner,proto3" json:"owner,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ItemFilter) Reset() {
	*x = ItemFilter{}
	mi := &file_MainServer_Item_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemFilter) ProtoMessage() {}

func (x *ItemFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemFilter.ProtoReflect.Descriptor instead.
func (*ItemFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{2}
}

func (x *ItemFilter) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *ItemFilter) GetSort() ItemFilterSort {
	if x != nil {
		return x.Sort
	}
	return ItemFilterSort_price_i
}

func (x *ItemFilter) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ItemFilter) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ItemFilter) GetMinPrice() float64 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

func (x *ItemFilter) GetMaxPrice() float64 {
	if x != nil {
		return x.MaxPrice
	}
	return 0
}

func (x *ItemFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *ItemFilter) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *ItemFilter) GetOwner() bool {
	if x != nil {
		return x.Owner
	}
	return false
}

type UseItemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	TargetPokeId  int64                  `protobuf:"varint,3,opt,name=targetPokeId,proto3" json:"targetPokeId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseItemInfo) Reset() {
	*x = UseItemInfo{}
	mi := &file_MainServer_Item_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemInfo) ProtoMessage() {}

func (x *UseItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemInfo.ProtoReflect.Descriptor instead.
func (*UseItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{3}
}

func (x *UseItemInfo) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseItemInfo) GetTargetPokeId() int64 {
	if x != nil {
		return x.TargetPokeId
	}
	return 0
}

type UseItemInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemName      string                 `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity      int32                  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	TargetPoke    *Poke                  `protobuf:"bytes,3,opt,name=targetPoke,proto3" json:"targetPoke,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UseItemInfoResponse) Reset() {
	*x = UseItemInfoResponse{}
	mi := &file_MainServer_Item_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseItemInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemInfoResponse) ProtoMessage() {}

func (x *UseItemInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemInfoResponse.ProtoReflect.Descriptor instead.
func (*UseItemInfoResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{4}
}

func (x *UseItemInfoResponse) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemInfoResponse) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseItemInfoResponse) GetTargetPoke() *Poke {
	if x != nil {
		return x.TargetPoke
	}
	return nil
}

type LocalItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Cost          int32                  `protobuf:"varint,2,opt,name=cost,proto3" json:"cost,omitempty"`
	Type          InventoryType          `protobuf:"varint,3,opt,name=type,proto3,enum=MainServer.InventoryType" json:"type,omitempty"`
	Duration      int32                  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Teams         []TrainerTeam          `protobuf:"varint,5,rep,packed,name=teams,proto3,enum=MainServer.TrainerTeam" json:"teams,omitempty"`
	TeamCost      int32                  `protobuf:"varint,6,opt,name=team_cost,json=teamCost,proto3" json:"team_cost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalItem) Reset() {
	*x = LocalItem{}
	mi := &file_MainServer_Item_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalItem) ProtoMessage() {}

func (x *LocalItem) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalItem.ProtoReflect.Descriptor instead.
func (*LocalItem) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{5}
}

func (x *LocalItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LocalItem) GetCost() int32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *LocalItem) GetType() InventoryType {
	if x != nil {
		return x.Type
	}
	return InventoryType_inventory_nor
}

func (x *LocalItem) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *LocalItem) GetTeams() []TrainerTeam {
	if x != nil {
		return x.Teams
	}
	return nil
}

func (x *LocalItem) GetTeamCost() int32 {
	if x != nil {
		return x.TeamCost
	}
	return 0
}

var File_MainServer_Item_proto protoreflect.FileDescriptor

const file_MainServer_Item_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Item.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\x1a\x1aMainServer/Inventory.proto\x1a\x1cMainServer/TrainerTeam.proto\"\xee\x01\n" +
	"\x04Item\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x14\n" +
	"\x05count\x18\x05 \x01(\x05R\x05count\x12\x14\n" +
	"\x05price\x18\x06 \x01(\x01R\x05price\x12\x1d\n" +
	"\n" +
	"sale_count\x18\a \x01(\x05R\tsaleCount\x12+\n" +
	"\x05extra\x18\b \x01(\v2\x15.MainServer.ItemExtraR\x05extra\x12\x1b\n" +
	"\tcreate_ts\x18\t \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\n" +
	" \x01(\x03R\bupdateTs\"\v\n" +
	"\tItemExtra\"\x84\x02\n" +
	"\n" +
	"ItemFilter\x12\x14\n" +
	"\x05names\x18\x01 \x03(\tR\x05names\x12.\n" +
	"\x04sort\x18\x02 \x01(\x0e2\x1a.MainServer.ItemFilterSortR\x04sort\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1b\n" +
	"\tmin_price\x18\x05 \x01(\x01R\bminPrice\x12\x1b\n" +
	"\tmax_price\x18\x06 \x01(\x01R\bmaxPrice\x12\x1b\n" +
	"\tupdate_ts\x18\a \x01(\x03R\bupdateTs\x12\x12\n" +
	"\x04sale\x18\b \x01(\bR\x04sale\x12\x14\n" +
	"\x05owner\x18\r \x01(\bR\x05owner\"j\n" +
	"\vUseItemInfo\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\x05R\bquantity\x12\"\n" +
	"\ftargetPokeId\x18\x03 \x01(\x03R\ftargetPokeId\"\x80\x01\n" +
	"\x13UseItemInfoResponse\x12\x1b\n" +
	"\titem_name\x18\x01 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\x05R\bquantity\x120\n" +
	"\n" +
	"targetPoke\x18\x03 \x01(\v2\x10.MainServer.PokeR\n" +
	"targetPoke\"\xca\x01\n" +
	"\tLocalItem\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04cost\x18\x02 \x01(\x05R\x04cost\x12-\n" +
	"\x04type\x18\x03 \x01(\x0e2\x19.MainServer.InventoryTypeR\x04type\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x05R\bduration\x12-\n" +
	"\x05teams\x18\x05 \x03(\x0e2\x17.MainServer.TrainerTeamR\x05teams\x12\x1b\n" +
	"\tteam_cost\x18\x06 \x01(\x05R\bteamCost*\x1d\n" +
	"\x0eItemFilterSort\x12\v\n" +
	"\aprice_i\x10\x00B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Item_proto_rawDescOnce sync.Once
	file_MainServer_Item_proto_rawDescData []byte
)

func file_MainServer_Item_proto_rawDescGZIP() []byte {
	file_MainServer_Item_proto_rawDescOnce.Do(func() {
		file_MainServer_Item_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Item_proto_rawDesc), len(file_MainServer_Item_proto_rawDesc)))
	})
	return file_MainServer_Item_proto_rawDescData
}

var file_MainServer_Item_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Item_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_MainServer_Item_proto_goTypes = []any{
	(ItemFilterSort)(0),         // 0: MainServer.ItemFilterSort
	(*Item)(nil),                // 1: MainServer.Item
	(*ItemExtra)(nil),           // 2: MainServer.ItemExtra
	(*ItemFilter)(nil),          // 3: MainServer.ItemFilter
	(*UseItemInfo)(nil),         // 4: MainServer.UseItemInfo
	(*UseItemInfoResponse)(nil), // 5: MainServer.UseItemInfoResponse
	(*LocalItem)(nil),           // 6: MainServer.LocalItem
	(*Poke)(nil),                // 7: MainServer.Poke
	(InventoryType)(0),          // 8: MainServer.InventoryType
	(TrainerTeam)(0),            // 9: MainServer.TrainerTeam
}
var file_MainServer_Item_proto_depIdxs = []int32{
	2, // 0: MainServer.Item.extra:type_name -> MainServer.ItemExtra
	0, // 1: MainServer.ItemFilter.sort:type_name -> MainServer.ItemFilterSort
	7, // 2: MainServer.UseItemInfoResponse.targetPoke:type_name -> MainServer.Poke
	8, // 3: MainServer.LocalItem.type:type_name -> MainServer.InventoryType
	9, // 4: MainServer.LocalItem.teams:type_name -> MainServer.TrainerTeam
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_MainServer_Item_proto_init() }
func file_MainServer_Item_proto_init() {
	if File_MainServer_Item_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_TrainerTeam_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Item_proto_rawDesc), len(file_MainServer_Item_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Item_proto_goTypes,
		DependencyIndexes: file_MainServer_Item_proto_depIdxs,
		EnumInfos:         file_MainServer_Item_proto_enumTypes,
		MessageInfos:      file_MainServer_Item_proto_msgTypes,
	}.Build()
	File_MainServer_Item_proto = out.File
	file_MainServer_Item_proto_goTypes = nil
	file_MainServer_Item_proto_depIdxs = nil
}
