// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Poke.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeLiveStatus int32

const (
	// PokeLiveStatus_Unknown = 0;
	PokeLiveStatus_PokeLiveStatus_Normal    PokeLiveStatus = 0
	PokeLiveStatus_PokeLiveStatus_Fainted   PokeLiveStatus = 1
	PokeLiveStatus_PokeLiveStatus_Frozen    PokeLiveStatus = 2
	PokeLiveStatus_PokeLiveStatus_Poisoned  PokeLiveStatus = 3
	PokeLiveStatus_PokeLiveStatus_Burned    PokeLiveStatus = 4
	PokeLiveStatus_PokeLiveStatus_Paralyzed PokeLiveStatus = 5
	PokeLiveStatus_PokeLiveStatus_Sleeping  PokeLiveStatus = 6
	PokeLiveStatus_PokeLiveStatus_Confused  PokeLiveStatus = 7
	PokeLiveStatus_PokeLiveStatus_Dead      PokeLiveStatus = 8 //死亡 //比如生育后
)

// Enum value maps for PokeLiveStatus.
var (
	PokeLiveStatus_name = map[int32]string{
		0: "PokeLiveStatus_Normal",
		1: "PokeLiveStatus_Fainted",
		2: "PokeLiveStatus_Frozen",
		3: "PokeLiveStatus_Poisoned",
		4: "PokeLiveStatus_Burned",
		5: "PokeLiveStatus_Paralyzed",
		6: "PokeLiveStatus_Sleeping",
		7: "PokeLiveStatus_Confused",
		8: "PokeLiveStatus_Dead",
	}
	PokeLiveStatus_value = map[string]int32{
		"PokeLiveStatus_Normal":    0,
		"PokeLiveStatus_Fainted":   1,
		"PokeLiveStatus_Frozen":    2,
		"PokeLiveStatus_Poisoned":  3,
		"PokeLiveStatus_Burned":    4,
		"PokeLiveStatus_Paralyzed": 5,
		"PokeLiveStatus_Sleeping":  6,
		"PokeLiveStatus_Confused":  7,
		"PokeLiveStatus_Dead":      8,
	}
)

func (x PokeLiveStatus) Enum() *PokeLiveStatus {
	p := new(PokeLiveStatus)
	*p = x
	return p
}

func (x PokeLiveStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeLiveStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[0].Descriptor()
}

func (PokeLiveStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[0]
}

func (x PokeLiveStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeLiveStatus.Descriptor instead.
func (PokeLiveStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{0}
}

type PokeTypeEnum int32

const (
	PokeTypeEnum_PokeType_Unknown  PokeTypeEnum = 0
	PokeTypeEnum_PokeType_Normal   PokeTypeEnum = 1
	PokeTypeEnum_PokeType_Fire     PokeTypeEnum = 2
	PokeTypeEnum_PokeType_Water    PokeTypeEnum = 3
	PokeTypeEnum_PokeType_Electric PokeTypeEnum = 4
	PokeTypeEnum_PokeType_Grass    PokeTypeEnum = 5
	PokeTypeEnum_PokeType_Ice      PokeTypeEnum = 6
	PokeTypeEnum_PokeType_Fighting PokeTypeEnum = 7
	PokeTypeEnum_PokeType_Poison   PokeTypeEnum = 8
	PokeTypeEnum_PokeType_Ground   PokeTypeEnum = 9
	PokeTypeEnum_PokeType_Flying   PokeTypeEnum = 10
	PokeTypeEnum_PokeType_Psychic  PokeTypeEnum = 11
	PokeTypeEnum_PokeType_Bug      PokeTypeEnum = 12
	PokeTypeEnum_PokeType_Rock     PokeTypeEnum = 13
	PokeTypeEnum_PokeType_Ghost    PokeTypeEnum = 14
	PokeTypeEnum_PokeType_Dragon   PokeTypeEnum = 15
	PokeTypeEnum_PokeType_Dark     PokeTypeEnum = 16
	PokeTypeEnum_PokeType_Steel    PokeTypeEnum = 17
	PokeTypeEnum_PokeType_Fairy    PokeTypeEnum = 18
	PokeTypeEnum_PokeType_Stellar  PokeTypeEnum = 19
)

// Enum value maps for PokeTypeEnum.
var (
	PokeTypeEnum_name = map[int32]string{
		0:  "PokeType_Unknown",
		1:  "PokeType_Normal",
		2:  "PokeType_Fire",
		3:  "PokeType_Water",
		4:  "PokeType_Electric",
		5:  "PokeType_Grass",
		6:  "PokeType_Ice",
		7:  "PokeType_Fighting",
		8:  "PokeType_Poison",
		9:  "PokeType_Ground",
		10: "PokeType_Flying",
		11: "PokeType_Psychic",
		12: "PokeType_Bug",
		13: "PokeType_Rock",
		14: "PokeType_Ghost",
		15: "PokeType_Dragon",
		16: "PokeType_Dark",
		17: "PokeType_Steel",
		18: "PokeType_Fairy",
		19: "PokeType_Stellar",
	}
	PokeTypeEnum_value = map[string]int32{
		"PokeType_Unknown":  0,
		"PokeType_Normal":   1,
		"PokeType_Fire":     2,
		"PokeType_Water":    3,
		"PokeType_Electric": 4,
		"PokeType_Grass":    5,
		"PokeType_Ice":      6,
		"PokeType_Fighting": 7,
		"PokeType_Poison":   8,
		"PokeType_Ground":   9,
		"PokeType_Flying":   10,
		"PokeType_Psychic":  11,
		"PokeType_Bug":      12,
		"PokeType_Rock":     13,
		"PokeType_Ghost":    14,
		"PokeType_Dragon":   15,
		"PokeType_Dark":     16,
		"PokeType_Steel":    17,
		"PokeType_Fairy":    18,
		"PokeType_Stellar":  19,
	}
)

func (x PokeTypeEnum) Enum() *PokeTypeEnum {
	p := new(PokeTypeEnum)
	*p = x
	return p
}

func (x PokeTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[1].Descriptor()
}

func (PokeTypeEnum) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[1]
}

func (x PokeTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeTypeEnum.Descriptor instead.
func (PokeTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{1}
}

type Nature int32

const (
	Nature_NATURE_UNSPECIFIED Nature = 0  // 默认值
	Nature_ADAMANT            Nature = 1  // +atk, -spa
	Nature_BASHFUL            Nature = 2  // 无增减
	Nature_BOLD               Nature = 3  // +def, -atk
	Nature_BRAVE              Nature = 4  // +atk, -spe
	Nature_CALM               Nature = 5  // +spd, -atk
	Nature_CAREFUL            Nature = 6  // +spd, -spa
	Nature_DOCILE             Nature = 7  // 无增减
	Nature_GENTLE             Nature = 8  // +spd, -def
	Nature_HARDY              Nature = 9  // 无增减
	Nature_HASTY              Nature = 10 // +spe, -def
	Nature_IMPISH             Nature = 11 // +def, -spa
	Nature_JOLLY              Nature = 12 // +spe, -spa
	Nature_LAX                Nature = 13 // +def, -spd
	Nature_LONELY             Nature = 14 // +atk, -def
	Nature_MILD               Nature = 15 // +spa, -def
	Nature_MODEST             Nature = 16 // +spa, -atk
	Nature_NAIVE              Nature = 17 // +spe, -spd
	Nature_NAUGHTY            Nature = 18 // +atk, -spd
	Nature_QUIET              Nature = 19 // +spa, -spe
	Nature_QUIRKY             Nature = 20 // 无增减
	Nature_RASH               Nature = 21 // +spa, -spd
	Nature_RELAXED            Nature = 22 // +def, -spe
	Nature_SASSY              Nature = 23 // +spd, -spe
	Nature_SERIOUS            Nature = 24 // 无增减
	Nature_TIMID              Nature = 25 // +spe, -atk
)

// Enum value maps for Nature.
var (
	Nature_name = map[int32]string{
		0:  "NATURE_UNSPECIFIED",
		1:  "ADAMANT",
		2:  "BASHFUL",
		3:  "BOLD",
		4:  "BRAVE",
		5:  "CALM",
		6:  "CAREFUL",
		7:  "DOCILE",
		8:  "GENTLE",
		9:  "HARDY",
		10: "HASTY",
		11: "IMPISH",
		12: "JOLLY",
		13: "LAX",
		14: "LONELY",
		15: "MILD",
		16: "MODEST",
		17: "NAIVE",
		18: "NAUGHTY",
		19: "QUIET",
		20: "QUIRKY",
		21: "RASH",
		22: "RELAXED",
		23: "SASSY",
		24: "SERIOUS",
		25: "TIMID",
	}
	Nature_value = map[string]int32{
		"NATURE_UNSPECIFIED": 0,
		"ADAMANT":            1,
		"BASHFUL":            2,
		"BOLD":               3,
		"BRAVE":              4,
		"CALM":               5,
		"CAREFUL":            6,
		"DOCILE":             7,
		"GENTLE":             8,
		"HARDY":              9,
		"HASTY":              10,
		"IMPISH":             11,
		"JOLLY":              12,
		"LAX":                13,
		"LONELY":             14,
		"MILD":               15,
		"MODEST":             16,
		"NAIVE":              17,
		"NAUGHTY":            18,
		"QUIET":              19,
		"QUIRKY":             20,
		"RASH":               21,
		"RELAXED":            22,
		"SASSY":              23,
		"SERIOUS":            24,
		"TIMID":              25,
	}
)

func (x Nature) Enum() *Nature {
	p := new(Nature)
	*p = x
	return p
}

func (x Nature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Nature) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[2].Descriptor()
}

func (Nature) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[2]
}

func (x Nature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Nature.Descriptor instead.
func (Nature) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{2}
}

type Gender int32

const (
	Gender_GenderNull Gender = 0
	Gender_M          Gender = 1
	Gender_F          Gender = 2
	Gender_N          Gender = 3
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "GenderNull",
		1: "M",
		2: "F",
		3: "N",
	}
	Gender_value = map[string]int32{
		"GenderNull": 0,
		"M":          1,
		"F":          2,
		"N":          3,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[3].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[3]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{3}
}

type PokemonStatKey int32

const (
	PokemonStatKey_StatKey_UNSPECIFIED    PokemonStatKey = 0
	PokemonStatKey_StatKey_HP             PokemonStatKey = 1
	PokemonStatKey_StatKey_Attack         PokemonStatKey = 2
	PokemonStatKey_StatKey_Defense        PokemonStatKey = 3
	PokemonStatKey_StatKey_SpecialAttack  PokemonStatKey = 4
	PokemonStatKey_StatKey_SpecialDefense PokemonStatKey = 5
	PokemonStatKey_StatKey_Speed          PokemonStatKey = 6
	PokemonStatKey_StatKey_Accuracy       PokemonStatKey = 7
	PokemonStatKey_StatKey_Evasion        PokemonStatKey = 8
)

// Enum value maps for PokemonStatKey.
var (
	PokemonStatKey_name = map[int32]string{
		0: "StatKey_UNSPECIFIED",
		1: "StatKey_HP",
		2: "StatKey_Attack",
		3: "StatKey_Defense",
		4: "StatKey_SpecialAttack",
		5: "StatKey_SpecialDefense",
		6: "StatKey_Speed",
		7: "StatKey_Accuracy",
		8: "StatKey_Evasion",
	}
	PokemonStatKey_value = map[string]int32{
		"StatKey_UNSPECIFIED":    0,
		"StatKey_HP":             1,
		"StatKey_Attack":         2,
		"StatKey_Defense":        3,
		"StatKey_SpecialAttack":  4,
		"StatKey_SpecialDefense": 5,
		"StatKey_Speed":          6,
		"StatKey_Accuracy":       7,
		"StatKey_Evasion":        8,
	}
)

func (x PokemonStatKey) Enum() *PokemonStatKey {
	p := new(PokemonStatKey)
	*p = x
	return p
}

func (x PokemonStatKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokemonStatKey) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[4].Descriptor()
}

func (PokemonStatKey) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[4]
}

func (x PokemonStatKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokemonStatKey.Descriptor instead.
func (PokemonStatKey) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{4}
}

type Poke struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Id       int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid      int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                           // user 信息
	Name     string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                          // 宝可梦名称
	NickName string                 `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`  // 宝可梦昵称
	BallName string                 `protobuf:"bytes,5,opt,name=ball_name,json=ballName,proto3" json:"ball_name,omitempty"`  // 捕获使用的球
	ItemName string                 `protobuf:"bytes,6,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`  // 携带的道具名称 //专属训练师的道具加个后缀_only
	Ability  string                 `protobuf:"bytes,7,opt,name=ability,proto3" json:"ability,omitempty"`                    // 特性
	Evs      *PokeStat              `protobuf:"bytes,8,opt,name=evs,proto3" json:"evs,omitempty"`                            // 宝可梦的努力值
	Ivs      *PokeStat              `protobuf:"bytes,9,opt,name=ivs,proto3" json:"ivs,omitempty"`                            // 宝可梦的个体值
	Sale     bool                   `protobuf:"varint,10,opt,name=sale,proto3" json:"sale,omitempty"`                        // 是否在售
	SaleInfo *SaleInfo              `protobuf:"bytes,11,opt,name=sale_info,json=saleInfo,proto3" json:"sale_info,omitempty"` // 售价
	// string stats = 12;  // 宝可梦当前状态
	BorrowInfo    *PokeBorrowInfo   `protobuf:"bytes,12,opt,name=borrow_info,json=borrowInfo,proto3" json:"borrow_info,omitempty"` // 宝可梦当前状态
	Moves         []*PokeSimpleMove `protobuf:"bytes,13,rep,name=moves,proto3" json:"moves,omitempty"`                             // 宝可梦的招式
	Level         int32             `protobuf:"varint,14,opt,name=level,proto3" json:"level,omitempty"`                            // 等级
	Nature        Nature            `protobuf:"varint,15,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`   // 宝可梦性格信息
	Status        *PokeStatusInfo   `protobuf:"bytes,16,opt,name=status,proto3" json:"status,omitempty"`                           // 宝可梦状态信息
	Experience    int64             `protobuf:"varint,17,opt,name=experience,proto3" json:"experience,omitempty"`                  // 经验值
	Born          *BornInfo         `protobuf:"bytes,18,opt,name=born,proto3" json:"born,omitempty"`                               // 出生信息
	Egg           bool              `protobuf:"varint,19,opt,name=egg,proto3" json:"egg,omitempty"`                                // 是否为蛋
	Shiny         int32             `protobuf:"varint,20,opt,name=shiny,proto3" json:"shiny,omitempty"`                            // 是否为异色（0为普通）
	Gender        Gender            `protobuf:"varint,21,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`   // 性别
	HpSub         int32             `protobuf:"varint,22,opt,name=hp_sub,json=hpSub,proto3" json:"hp_sub,omitempty"`               // hp
	Happiness     int32             `protobuf:"varint,23,opt,name=happiness,proto3" json:"happiness,omitempty"`
	SysExtra      *PokeSysExtra     `protobuf:"bytes,24,opt,name=sys_extra,json=sysExtra,proto3" json:"sys_extra,omitempty"`
	Extra         *PokeExtra        `protobuf:"bytes,25,opt,name=extra,proto3" json:"extra,omitempty"` // 额外信息
	Release       bool              `protobuf:"varint,26,opt,name=release,proto3" json:"release,omitempty"`
	HonorInfo     *HonorInfo        `protobuf:"bytes,27,opt,name=honorInfo,proto3" json:"honorInfo,omitempty"`
	BreedCount    int32             `protobuf:"varint,28,opt,name=breedCount,proto3" json:"breedCount,omitempty"`
	CreateTs      int64             `protobuf:"varint,29,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"` // 创建时间戳
	UpdateTs      int64             `protobuf:"varint,30,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Poke) Reset() {
	*x = Poke{}
	mi := &file_MainServer_Poke_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Poke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Poke) ProtoMessage() {}

func (x *Poke) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Poke.ProtoReflect.Descriptor instead.
func (*Poke) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{0}
}

func (x *Poke) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Poke) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Poke) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Poke) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *Poke) GetBallName() string {
	if x != nil {
		return x.BallName
	}
	return ""
}

func (x *Poke) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *Poke) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *Poke) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

func (x *Poke) GetIvs() *PokeStat {
	if x != nil {
		return x.Ivs
	}
	return nil
}

func (x *Poke) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *Poke) GetSaleInfo() *SaleInfo {
	if x != nil {
		return x.SaleInfo
	}
	return nil
}

func (x *Poke) GetBorrowInfo() *PokeBorrowInfo {
	if x != nil {
		return x.BorrowInfo
	}
	return nil
}

func (x *Poke) GetMoves() []*PokeSimpleMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

func (x *Poke) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Poke) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

func (x *Poke) GetStatus() *PokeStatusInfo {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *Poke) GetExperience() int64 {
	if x != nil {
		return x.Experience
	}
	return 0
}

func (x *Poke) GetBorn() *BornInfo {
	if x != nil {
		return x.Born
	}
	return nil
}

func (x *Poke) GetEgg() bool {
	if x != nil {
		return x.Egg
	}
	return false
}

func (x *Poke) GetShiny() int32 {
	if x != nil {
		return x.Shiny
	}
	return 0
}

func (x *Poke) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

func (x *Poke) GetHpSub() int32 {
	if x != nil {
		return x.HpSub
	}
	return 0
}

func (x *Poke) GetHappiness() int32 {
	if x != nil {
		return x.Happiness
	}
	return 0
}

func (x *Poke) GetSysExtra() *PokeSysExtra {
	if x != nil {
		return x.SysExtra
	}
	return nil
}

func (x *Poke) GetExtra() *PokeExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Poke) GetRelease() bool {
	if x != nil {
		return x.Release
	}
	return false
}

func (x *Poke) GetHonorInfo() *HonorInfo {
	if x != nil {
		return x.HonorInfo
	}
	return nil
}

func (x *Poke) GetBreedCount() int32 {
	if x != nil {
		return x.BreedCount
	}
	return 0
}

func (x *Poke) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Poke) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type PokeStatusInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LiveStatus    PokeLiveStatus         `protobuf:"varint,1,opt,name=live_status,json=liveStatus,proto3,enum=MainServer.PokeLiveStatus" json:"live_status,omitempty"` // 生命状态
	IsOnly        bool                   `protobuf:"varint,2,opt,name=is_only,json=isOnly,proto3" json:"is_only,omitempty"`                                            // 是否为专属训练师的宝可梦
	Sealed        string                 `protobuf:"bytes,3,opt,name=sealed,proto3" json:"sealed,omitempty"`                                                           // 密封
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeStatusInfo) Reset() {
	*x = PokeStatusInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeStatusInfo) ProtoMessage() {}

func (x *PokeStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeStatusInfo.ProtoReflect.Descriptor instead.
func (*PokeStatusInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{1}
}

func (x *PokeStatusInfo) GetLiveStatus() PokeLiveStatus {
	if x != nil {
		return x.LiveStatus
	}
	return PokeLiveStatus_PokeLiveStatus_Normal
}

func (x *PokeStatusInfo) GetIsOnly() bool {
	if x != nil {
		return x.IsOnly
	}
	return false
}

func (x *PokeStatusInfo) GetSealed() string {
	if x != nil {
		return x.Sealed
	}
	return ""
}

type PokeBorrowInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BorrowTs      int64                  `protobuf:"varint,1,opt,name=borrow_ts,json=borrowTs,proto3" json:"borrow_ts,omitempty"`       // 借用时间
	ReturnTs      int64                  `protobuf:"varint,2,opt,name=return_ts,json=returnTs,proto3" json:"return_ts,omitempty"`       // 归还时间
	CanRenewed    bool                   `protobuf:"varint,3,opt,name=can_renewed,json=canRenewed,proto3" json:"can_renewed,omitempty"` // 是否可续借
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBorrowInfo) Reset() {
	*x = PokeBorrowInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBorrowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBorrowInfo) ProtoMessage() {}

func (x *PokeBorrowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBorrowInfo.ProtoReflect.Descriptor instead.
func (*PokeBorrowInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{2}
}

func (x *PokeBorrowInfo) GetBorrowTs() int64 {
	if x != nil {
		return x.BorrowTs
	}
	return 0
}

func (x *PokeBorrowInfo) GetReturnTs() int64 {
	if x != nil {
		return x.ReturnTs
	}
	return 0
}

func (x *PokeBorrowInfo) GetCanRenewed() bool {
	if x != nil {
		return x.CanRenewed
	}
	return false
}

type HonorInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Badges        []string               `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HonorInfo) Reset() {
	*x = HonorInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HonorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HonorInfo) ProtoMessage() {}

func (x *HonorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HonorInfo.ProtoReflect.Descriptor instead.
func (*HonorInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{3}
}

func (x *HonorInfo) GetBadges() []string {
	if x != nil {
		return x.Badges
	}
	return nil
}

type SaleInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         int32                  `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	SpecialCoin   int32                  `protobuf:"varint,2,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`
	CreateTs      int64                  `protobuf:"varint,3,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"` // 上架时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaleInfo) Reset() {
	*x = SaleInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleInfo) ProtoMessage() {}

func (x *SaleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleInfo.ProtoReflect.Descriptor instead.
func (*SaleInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{4}
}

func (x *SaleInfo) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SaleInfo) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

func (x *SaleInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

type PokeStat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hp            int32                  `protobuf:"varint,1,opt,name=hp,proto3" json:"hp,omitempty"`
	Atk           int32                  `protobuf:"varint,2,opt,name=atk,proto3" json:"atk,omitempty"`
	Def           int32                  `protobuf:"varint,3,opt,name=def,proto3" json:"def,omitempty"`
	Spa           int32                  `protobuf:"varint,4,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd           int32                  `protobuf:"varint,5,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe           int32                  `protobuf:"varint,6,opt,name=spe,proto3" json:"spe,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeStat) Reset() {
	*x = PokeStat{}
	mi := &file_MainServer_Poke_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeStat) ProtoMessage() {}

func (x *PokeStat) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeStat.ProtoReflect.Descriptor instead.
func (*PokeStat) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{5}
}

func (x *PokeStat) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PokeStat) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PokeStat) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PokeStat) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PokeStat) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PokeStat) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

type PokeSimpleMove struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PpPro         int32                  `protobuf:"varint,2,opt,name=pp_pro,json=ppPro,proto3" json:"pp_pro,omitempty"`
	PpSub         int32                  `protobuf:"varint,3,opt,name=pp_sub,json=ppSub,proto3" json:"pp_sub,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeSimpleMove) Reset() {
	*x = PokeSimpleMove{}
	mi := &file_MainServer_Poke_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeSimpleMove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeSimpleMove) ProtoMessage() {}

func (x *PokeSimpleMove) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeSimpleMove.ProtoReflect.Descriptor instead.
func (*PokeSimpleMove) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{6}
}

func (x *PokeSimpleMove) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeSimpleMove) GetPpPro() int32 {
	if x != nil {
		return x.PpPro
	}
	return 0
}

func (x *PokeSimpleMove) GetPpSub() int32 {
	if x != nil {
		return x.PpSub
	}
	return 0
}

type BornInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RegionInfo    string                 `protobuf:"bytes,1,opt,name=region_info,json=regionInfo,proto3" json:"region_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BornInfo) Reset() {
	*x = BornInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BornInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BornInfo) ProtoMessage() {}

func (x *BornInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BornInfo.ProtoReflect.Descriptor instead.
func (*BornInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{7}
}

func (x *BornInfo) GetRegionInfo() string {
	if x != nil {
		return x.RegionInfo
	}
	return ""
}

type PokeSysExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DynamaxLevel  int32                  `protobuf:"varint,1,opt,name=dynamaxLevel,proto3" json:"dynamaxLevel,omitempty"`
	Gigantamax    bool                   `protobuf:"varint,2,opt,name=gigantamax,proto3" json:"gigantamax,omitempty"`
	Terastal      string                 `protobuf:"bytes,3,opt,name=terastal,proto3" json:"terastal,omitempty"` //太晶化类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeSysExtra) Reset() {
	*x = PokeSysExtra{}
	mi := &file_MainServer_Poke_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeSysExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeSysExtra) ProtoMessage() {}

func (x *PokeSysExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeSysExtra.ProtoReflect.Descriptor instead.
func (*PokeSysExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{8}
}

func (x *PokeSysExtra) GetDynamaxLevel() int32 {
	if x != nil {
		return x.DynamaxLevel
	}
	return 0
}

func (x *PokeSysExtra) GetGigantamax() bool {
	if x != nil {
		return x.Gigantamax
	}
	return false
}

func (x *PokeSysExtra) GetTerastal() string {
	if x != nil {
		return x.Terastal
	}
	return ""
}

type PokeExtra struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TempItem        string                 `protobuf:"bytes,1,opt,name=tempItem,proto3" json:"tempItem,omitempty"`               //临时道具 //用于租借方更换道具 //归还后退还道具
	TempEvs         *PokeStat              `protobuf:"bytes,2,opt,name=tempEvs,proto3" json:"tempEvs,omitempty"`                 // 临时宝可梦的努力值 //用于租借修改努力值 //待定使用
	LastLevelUpInfo *PokeLastLevelUpInfo   `protobuf:"bytes,3,opt,name=lastLevelUpInfo,proto3" json:"lastLevelUpInfo,omitempty"` //上次升级信息(也可能是进化)
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PokeExtra) Reset() {
	*x = PokeExtra{}
	mi := &file_MainServer_Poke_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeExtra) ProtoMessage() {}

func (x *PokeExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeExtra.ProtoReflect.Descriptor instead.
func (*PokeExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{9}
}

func (x *PokeExtra) GetTempItem() string {
	if x != nil {
		return x.TempItem
	}
	return ""
}

func (x *PokeExtra) GetTempEvs() *PokeStat {
	if x != nil {
		return x.TempEvs
	}
	return nil
}

func (x *PokeExtra) GetLastLevelUpInfo() *PokeLastLevelUpInfo {
	if x != nil {
		return x.LastLevelUpInfo
	}
	return nil
}

type PokeLastLevelUpInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ts            int64                  `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Level         int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Exp           int32                  `protobuf:"varint,3,opt,name=exp,proto3" json:"exp,omitempty"`
	IsEvolution   bool                   `protobuf:"varint,4,opt,name=isEvolution,proto3" json:"isEvolution,omitempty"`
	Moves         []string               `protobuf:"bytes,5,rep,name=moves,proto3" json:"moves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeLastLevelUpInfo) Reset() {
	*x = PokeLastLevelUpInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeLastLevelUpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeLastLevelUpInfo) ProtoMessage() {}

func (x *PokeLastLevelUpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeLastLevelUpInfo.ProtoReflect.Descriptor instead.
func (*PokeLastLevelUpInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{10}
}

func (x *PokeLastLevelUpInfo) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *PokeLastLevelUpInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PokeLastLevelUpInfo) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PokeLastLevelUpInfo) GetIsEvolution() bool {
	if x != nil {
		return x.IsEvolution
	}
	return false
}

func (x *PokeLastLevelUpInfo) GetMoves() []string {
	if x != nil {
		return x.Moves
	}
	return nil
}

type LevelUpLearnMoveRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PokeId          int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	LastLevelUpInfo *PokeLastLevelUpInfo   `protobuf:"bytes,2,opt,name=lastLevelUpInfo,proto3" json:"lastLevelUpInfo,omitempty"`
	Moves           []*PokeSimpleMove      `protobuf:"bytes,3,rep,name=moves,proto3" json:"moves,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *LevelUpLearnMoveRequest) Reset() {
	*x = LevelUpLearnMoveRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LevelUpLearnMoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelUpLearnMoveRequest) ProtoMessage() {}

func (x *LevelUpLearnMoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelUpLearnMoveRequest.ProtoReflect.Descriptor instead.
func (*LevelUpLearnMoveRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{11}
}

func (x *LevelUpLearnMoveRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *LevelUpLearnMoveRequest) GetLastLevelUpInfo() *PokeLastLevelUpInfo {
	if x != nil {
		return x.LastLevelUpInfo
	}
	return nil
}

func (x *LevelUpLearnMoveRequest) GetMoves() []*PokeSimpleMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

type RpcPokeUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUpdateRequest) Reset() {
	*x = RpcPokeUpdateRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUpdateRequest) ProtoMessage() {}

func (x *RpcPokeUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUpdateRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeUpdateRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{12}
}

type RpcPokeUpdateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUpdateResponse) Reset() {
	*x = RpcPokeUpdateResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUpdateResponse) ProtoMessage() {}

func (x *RpcPokeUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUpdateResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeUpdateResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{13}
}

type RpcPokeChangeEvsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Evs           *PokeStat              `protobuf:"bytes,2,opt,name=evs,proto3" json:"evs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeEvsRequest) Reset() {
	*x = RpcPokeChangeEvsRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeEvsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeEvsRequest) ProtoMessage() {}

func (x *RpcPokeChangeEvsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeEvsRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeEvsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{14}
}

func (x *RpcPokeChangeEvsRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeEvsRequest) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

type RpcPokeChangeEvsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Evs           *PokeStat              `protobuf:"bytes,2,opt,name=evs,proto3" json:"evs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeEvsResponse) Reset() {
	*x = RpcPokeChangeEvsResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeEvsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeEvsResponse) ProtoMessage() {}

func (x *RpcPokeChangeEvsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeEvsResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeEvsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{15}
}

func (x *RpcPokeChangeEvsResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeEvsResponse) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

type RpcPokeChangeAbilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Ability       string                 `protobuf:"bytes,2,opt,name=ability,proto3" json:"ability,omitempty"`
	UseCount      int32                  `protobuf:"varint,3,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeAbilityRequest) Reset() {
	*x = RpcPokeChangeAbilityRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeAbilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeAbilityRequest) ProtoMessage() {}

func (x *RpcPokeChangeAbilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeAbilityRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeAbilityRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{16}
}

func (x *RpcPokeChangeAbilityRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeAbilityRequest) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *RpcPokeChangeAbilityRequest) GetUseCount() int32 {
	if x != nil {
		return x.UseCount
	}
	return 0
}

type RpcPokeChangeAbilityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeAbilityResponse) Reset() {
	*x = RpcPokeChangeAbilityResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeAbilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeAbilityResponse) ProtoMessage() {}

func (x *RpcPokeChangeAbilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeAbilityResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeAbilityResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{17}
}

func (x *RpcPokeChangeAbilityResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeAbilityResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type RpcPokeChangeMoveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	MoveName      string                 `protobuf:"bytes,2,opt,name=move_name,json=moveName,proto3" json:"move_name,omitempty"`
	MoveIndex     int32                  `protobuf:"varint,3,opt,name=move_index,json=moveIndex,proto3" json:"move_index,omitempty"`
	IsRemove      bool                   `protobuf:"varint,4,opt,name=is_remove,json=isRemove,proto3" json:"is_remove,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeMoveRequest) Reset() {
	*x = RpcPokeChangeMoveRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeMoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeMoveRequest) ProtoMessage() {}

func (x *RpcPokeChangeMoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeMoveRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeMoveRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{18}
}

func (x *RpcPokeChangeMoveRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeMoveRequest) GetMoveName() string {
	if x != nil {
		return x.MoveName
	}
	return ""
}

func (x *RpcPokeChangeMoveRequest) GetMoveIndex() int32 {
	if x != nil {
		return x.MoveIndex
	}
	return 0
}

func (x *RpcPokeChangeMoveRequest) GetIsRemove() bool {
	if x != nil {
		return x.IsRemove
	}
	return false
}

type RpcPokeChangeMoveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Moves         []*PokeSimpleMove      `protobuf:"bytes,3,rep,name=moves,proto3" json:"moves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeMoveResponse) Reset() {
	*x = RpcPokeChangeMoveResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeMoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeMoveResponse) ProtoMessage() {}

func (x *RpcPokeChangeMoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeMoveResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeMoveResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{19}
}

func (x *RpcPokeChangeMoveResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeMoveResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcPokeChangeMoveResponse) GetMoves() []*PokeSimpleMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

type RpcPokeUseCapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	CapName       string                 `protobuf:"bytes,2,opt,name=cap_name,json=capName,proto3" json:"cap_name,omitempty"`
	StatKey       PokemonStatKey         `protobuf:"varint,3,opt,name=stat_key,json=statKey,proto3,enum=MainServer.PokemonStatKey" json:"stat_key,omitempty"`
	UseCount      int32                  `protobuf:"varint,4,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUseCapRequest) Reset() {
	*x = RpcPokeUseCapRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUseCapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUseCapRequest) ProtoMessage() {}

func (x *RpcPokeUseCapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUseCapRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeUseCapRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{20}
}

func (x *RpcPokeUseCapRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeUseCapRequest) GetCapName() string {
	if x != nil {
		return x.CapName
	}
	return ""
}

func (x *RpcPokeUseCapRequest) GetStatKey() PokemonStatKey {
	if x != nil {
		return x.StatKey
	}
	return PokemonStatKey_StatKey_UNSPECIFIED
}

func (x *RpcPokeUseCapRequest) GetUseCount() int32 {
	if x != nil {
		return x.UseCount
	}
	return 0
}

type RpcPokeUseCapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	UseResult     int32                  `protobuf:"varint,2,opt,name=use_result,json=useResult,proto3" json:"use_result,omitempty"`
	StatKey       PokemonStatKey         `protobuf:"varint,3,opt,name=stat_key,json=statKey,proto3,enum=MainServer.PokemonStatKey" json:"stat_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUseCapResponse) Reset() {
	*x = RpcPokeUseCapResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUseCapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUseCapResponse) ProtoMessage() {}

func (x *RpcPokeUseCapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUseCapResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeUseCapResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{21}
}

func (x *RpcPokeUseCapResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeUseCapResponse) GetUseResult() int32 {
	if x != nil {
		return x.UseResult
	}
	return 0
}

func (x *RpcPokeUseCapResponse) GetStatKey() PokemonStatKey {
	if x != nil {
		return x.StatKey
	}
	return PokemonStatKey_StatKey_UNSPECIFIED
}

type RpcPokeChangeNatureRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Nature        Nature                 `protobuf:"varint,2,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`
	UseCount      int32                  `protobuf:"varint,3,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeNatureRequest) Reset() {
	*x = RpcPokeChangeNatureRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeNatureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeNatureRequest) ProtoMessage() {}

func (x *RpcPokeChangeNatureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeNatureRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeNatureRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{22}
}

func (x *RpcPokeChangeNatureRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeNatureRequest) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

func (x *RpcPokeChangeNatureRequest) GetUseCount() int32 {
	if x != nil {
		return x.UseCount
	}
	return 0
}

type RpcPokeChangeNatureResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Nature        Nature                 `protobuf:"varint,3,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeNatureResponse) Reset() {
	*x = RpcPokeChangeNatureResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeNatureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeNatureResponse) ProtoMessage() {}

func (x *RpcPokeChangeNatureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeNatureResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeNatureResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{23}
}

func (x *RpcPokeChangeNatureResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeNatureResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcPokeChangeNatureResponse) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

var File_MainServer_Poke_proto protoreflect.FileDescriptor

const file_MainServer_Poke_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Poke.proto\x12\n" +
	"MainServer\"\x89\b\n" +
	"\x04Poke\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1b\n" +
	"\tnick_name\x18\x04 \x01(\tR\bnickName\x12\x1b\n" +
	"\tball_name\x18\x05 \x01(\tR\bballName\x12\x1b\n" +
	"\titem_name\x18\x06 \x01(\tR\bitemName\x12\x18\n" +
	"\aability\x18\a \x01(\tR\aability\x12&\n" +
	"\x03evs\x18\b \x01(\v2\x14.MainServer.PokeStatR\x03evs\x12&\n" +
	"\x03ivs\x18\t \x01(\v2\x14.MainServer.PokeStatR\x03ivs\x12\x12\n" +
	"\x04sale\x18\n" +
	" \x01(\bR\x04sale\x121\n" +
	"\tsale_info\x18\v \x01(\v2\x14.MainServer.SaleInfoR\bsaleInfo\x12;\n" +
	"\vborrow_info\x18\f \x01(\v2\x1a.MainServer.PokeBorrowInfoR\n" +
	"borrowInfo\x120\n" +
	"\x05moves\x18\r \x03(\v2\x1a.MainServer.PokeSimpleMoveR\x05moves\x12\x14\n" +
	"\x05level\x18\x0e \x01(\x05R\x05level\x12*\n" +
	"\x06nature\x18\x0f \x01(\x0e2\x12.MainServer.NatureR\x06nature\x122\n" +
	"\x06status\x18\x10 \x01(\v2\x1a.MainServer.PokeStatusInfoR\x06status\x12\x1e\n" +
	"\n" +
	"experience\x18\x11 \x01(\x03R\n" +
	"experience\x12(\n" +
	"\x04born\x18\x12 \x01(\v2\x14.MainServer.BornInfoR\x04born\x12\x10\n" +
	"\x03egg\x18\x13 \x01(\bR\x03egg\x12\x14\n" +
	"\x05shiny\x18\x14 \x01(\x05R\x05shiny\x12*\n" +
	"\x06gender\x18\x15 \x01(\x0e2\x12.MainServer.GenderR\x06gender\x12\x15\n" +
	"\x06hp_sub\x18\x16 \x01(\x05R\x05hpSub\x12\x1c\n" +
	"\thappiness\x18\x17 \x01(\x05R\thappiness\x125\n" +
	"\tsys_extra\x18\x18 \x01(\v2\x18.MainServer.PokeSysExtraR\bsysExtra\x12+\n" +
	"\x05extra\x18\x19 \x01(\v2\x15.MainServer.PokeExtraR\x05extra\x12\x18\n" +
	"\arelease\x18\x1a \x01(\bR\arelease\x123\n" +
	"\thonorInfo\x18\x1b \x01(\v2\x15.MainServer.HonorInfoR\thonorInfo\x12\x1e\n" +
	"\n" +
	"breedCount\x18\x1c \x01(\x05R\n" +
	"breedCount\x12\x1b\n" +
	"\tcreate_ts\x18\x1d \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x1e \x01(\x03R\bupdateTs\"~\n" +
	"\x0ePokeStatusInfo\x12;\n" +
	"\vlive_status\x18\x01 \x01(\x0e2\x1a.MainServer.PokeLiveStatusR\n" +
	"liveStatus\x12\x17\n" +
	"\ais_only\x18\x02 \x01(\bR\x06isOnly\x12\x16\n" +
	"\x06sealed\x18\x03 \x01(\tR\x06sealed\"k\n" +
	"\x0ePokeBorrowInfo\x12\x1b\n" +
	"\tborrow_ts\x18\x01 \x01(\x03R\bborrowTs\x12\x1b\n" +
	"\treturn_ts\x18\x02 \x01(\x03R\breturnTs\x12\x1f\n" +
	"\vcan_renewed\x18\x03 \x01(\bR\n" +
	"canRenewed\"#\n" +
	"\tHonorInfo\x12\x16\n" +
	"\x06badges\x18\x01 \x03(\tR\x06badges\"`\n" +
	"\bSaleInfo\x12\x14\n" +
	"\x05price\x18\x01 \x01(\x05R\x05price\x12!\n" +
	"\fspecial_coin\x18\x02 \x01(\x05R\vspecialCoin\x12\x1b\n" +
	"\tcreate_ts\x18\x03 \x01(\x03R\bcreateTs\"t\n" +
	"\bPokeStat\x12\x0e\n" +
	"\x02hp\x18\x01 \x01(\x05R\x02hp\x12\x10\n" +
	"\x03atk\x18\x02 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x03 \x01(\x05R\x03def\x12\x10\n" +
	"\x03spa\x18\x04 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x05 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\x06 \x01(\x05R\x03spe\"R\n" +
	"\x0ePokeSimpleMove\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x15\n" +
	"\x06pp_pro\x18\x02 \x01(\x05R\x05ppPro\x12\x15\n" +
	"\x06pp_sub\x18\x03 \x01(\x05R\x05ppSub\"+\n" +
	"\bBornInfo\x12\x1f\n" +
	"\vregion_info\x18\x01 \x01(\tR\n" +
	"regionInfo\"n\n" +
	"\fPokeSysExtra\x12\"\n" +
	"\fdynamaxLevel\x18\x01 \x01(\x05R\fdynamaxLevel\x12\x1e\n" +
	"\n" +
	"gigantamax\x18\x02 \x01(\bR\n" +
	"gigantamax\x12\x1a\n" +
	"\bterastal\x18\x03 \x01(\tR\bterastal\"\xa2\x01\n" +
	"\tPokeExtra\x12\x1a\n" +
	"\btempItem\x18\x01 \x01(\tR\btempItem\x12.\n" +
	"\atempEvs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\atempEvs\x12I\n" +
	"\x0flastLevelUpInfo\x18\x03 \x01(\v2\x1f.MainServer.PokeLastLevelUpInfoR\x0flastLevelUpInfo\"\x85\x01\n" +
	"\x13PokeLastLevelUpInfo\x12\x0e\n" +
	"\x02ts\x18\x01 \x01(\x03R\x02ts\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x05R\x05level\x12\x10\n" +
	"\x03exp\x18\x03 \x01(\x05R\x03exp\x12 \n" +
	"\visEvolution\x18\x04 \x01(\bR\visEvolution\x12\x14\n" +
	"\x05moves\x18\x05 \x03(\tR\x05moves\"\xaf\x01\n" +
	"\x17LevelUpLearnMoveRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12I\n" +
	"\x0flastLevelUpInfo\x18\x02 \x01(\v2\x1f.MainServer.PokeLastLevelUpInfoR\x0flastLevelUpInfo\x120\n" +
	"\x05moves\x18\x03 \x03(\v2\x1a.MainServer.PokeSimpleMoveR\x05moves\"\x16\n" +
	"\x14RpcPokeUpdateRequest\"\x17\n" +
	"\x15RpcPokeUpdateResponse\"Z\n" +
	"\x17RpcPokeChangeEvsRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12&\n" +
	"\x03evs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\x03evs\"[\n" +
	"\x18RpcPokeChangeEvsResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12&\n" +
	"\x03evs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\x03evs\"m\n" +
	"\x1bRpcPokeChangeAbilityRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\aability\x18\x02 \x01(\tR\aability\x12\x1b\n" +
	"\tuse_count\x18\x03 \x01(\x05R\buseCount\"Q\n" +
	"\x1cRpcPokeChangeAbilityResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\"\x8c\x01\n" +
	"\x18RpcPokeChangeMoveRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x1b\n" +
	"\tmove_name\x18\x02 \x01(\tR\bmoveName\x12\x1d\n" +
	"\n" +
	"move_index\x18\x03 \x01(\x05R\tmoveIndex\x12\x1b\n" +
	"\tis_remove\x18\x04 \x01(\bR\bisRemove\"\x80\x01\n" +
	"\x19RpcPokeChangeMoveResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x120\n" +
	"\x05moves\x18\x03 \x03(\v2\x1a.MainServer.PokeSimpleMoveR\x05moves\"\x9e\x01\n" +
	"\x14RpcPokeUseCapRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x19\n" +
	"\bcap_name\x18\x02 \x01(\tR\acapName\x125\n" +
	"\bstat_key\x18\x03 \x01(\x0e2\x1a.MainServer.PokemonStatKeyR\astatKey\x12\x1b\n" +
	"\tuse_count\x18\x04 \x01(\x05R\buseCount\"\x86\x01\n" +
	"\x15RpcPokeUseCapResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x1d\n" +
	"\n" +
	"use_result\x18\x02 \x01(\x05R\tuseResult\x125\n" +
	"\bstat_key\x18\x03 \x01(\x0e2\x1a.MainServer.PokemonStatKeyR\astatKey\"~\n" +
	"\x1aRpcPokeChangeNatureRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12*\n" +
	"\x06nature\x18\x02 \x01(\x0e2\x12.MainServer.NatureR\x06nature\x12\x1b\n" +
	"\tuse_count\x18\x03 \x01(\x05R\buseCount\"|\n" +
	"\x1bRpcPokeChangeNatureResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12*\n" +
	"\x06nature\x18\x03 \x01(\x0e2\x12.MainServer.NatureR\x06nature*\x8b\x02\n" +
	"\x0ePokeLiveStatus\x12\x19\n" +
	"\x15PokeLiveStatus_Normal\x10\x00\x12\x1a\n" +
	"\x16PokeLiveStatus_Fainted\x10\x01\x12\x19\n" +
	"\x15PokeLiveStatus_Frozen\x10\x02\x12\x1b\n" +
	"\x17PokeLiveStatus_Poisoned\x10\x03\x12\x19\n" +
	"\x15PokeLiveStatus_Burned\x10\x04\x12\x1c\n" +
	"\x18PokeLiveStatus_Paralyzed\x10\x05\x12\x1b\n" +
	"\x17PokeLiveStatus_Sleeping\x10\x06\x12\x1b\n" +
	"\x17PokeLiveStatus_Confused\x10\a\x12\x17\n" +
	"\x13PokeLiveStatus_Dead\x10\b*\xa8\x03\n" +
	"\fPokeTypeEnum\x12\x14\n" +
	"\x10PokeType_Unknown\x10\x00\x12\x13\n" +
	"\x0fPokeType_Normal\x10\x01\x12\x11\n" +
	"\rPokeType_Fire\x10\x02\x12\x12\n" +
	"\x0ePokeType_Water\x10\x03\x12\x15\n" +
	"\x11PokeType_Electric\x10\x04\x12\x12\n" +
	"\x0ePokeType_Grass\x10\x05\x12\x10\n" +
	"\fPokeType_Ice\x10\x06\x12\x15\n" +
	"\x11PokeType_Fighting\x10\a\x12\x13\n" +
	"\x0fPokeType_Poison\x10\b\x12\x13\n" +
	"\x0fPokeType_Ground\x10\t\x12\x13\n" +
	"\x0fPokeType_Flying\x10\n" +
	"\x12\x14\n" +
	"\x10PokeType_Psychic\x10\v\x12\x10\n" +
	"\fPokeType_Bug\x10\f\x12\x11\n" +
	"\rPokeType_Rock\x10\r\x12\x12\n" +
	"\x0ePokeType_Ghost\x10\x0e\x12\x13\n" +
	"\x0fPokeType_Dragon\x10\x0f\x12\x11\n" +
	"\rPokeType_Dark\x10\x10\x12\x12\n" +
	"\x0ePokeType_Steel\x10\x11\x12\x12\n" +
	"\x0ePokeType_Fairy\x10\x12\x12\x14\n" +
	"\x10PokeType_Stellar\x10\x13*\xbf\x02\n" +
	"\x06Nature\x12\x16\n" +
	"\x12NATURE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aADAMANT\x10\x01\x12\v\n" +
	"\aBASHFUL\x10\x02\x12\b\n" +
	"\x04BOLD\x10\x03\x12\t\n" +
	"\x05BRAVE\x10\x04\x12\b\n" +
	"\x04CALM\x10\x05\x12\v\n" +
	"\aCAREFUL\x10\x06\x12\n" +
	"\n" +
	"\x06DOCILE\x10\a\x12\n" +
	"\n" +
	"\x06GENTLE\x10\b\x12\t\n" +
	"\x05HARDY\x10\t\x12\t\n" +
	"\x05HASTY\x10\n" +
	"\x12\n" +
	"\n" +
	"\x06IMPISH\x10\v\x12\t\n" +
	"\x05JOLLY\x10\f\x12\a\n" +
	"\x03LAX\x10\r\x12\n" +
	"\n" +
	"\x06LONELY\x10\x0e\x12\b\n" +
	"\x04MILD\x10\x0f\x12\n" +
	"\n" +
	"\x06MODEST\x10\x10\x12\t\n" +
	"\x05NAIVE\x10\x11\x12\v\n" +
	"\aNAUGHTY\x10\x12\x12\t\n" +
	"\x05QUIET\x10\x13\x12\n" +
	"\n" +
	"\x06QUIRKY\x10\x14\x12\b\n" +
	"\x04RASH\x10\x15\x12\v\n" +
	"\aRELAXED\x10\x16\x12\t\n" +
	"\x05SASSY\x10\x17\x12\v\n" +
	"\aSERIOUS\x10\x18\x12\t\n" +
	"\x05TIMID\x10\x19*-\n" +
	"\x06Gender\x12\x0e\n" +
	"\n" +
	"GenderNull\x10\x00\x12\x05\n" +
	"\x01M\x10\x01\x12\x05\n" +
	"\x01F\x10\x02\x12\x05\n" +
	"\x01N\x10\x03*\xd7\x01\n" +
	"\x0ePokemonStatKey\x12\x17\n" +
	"\x13StatKey_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"StatKey_HP\x10\x01\x12\x12\n" +
	"\x0eStatKey_Attack\x10\x02\x12\x13\n" +
	"\x0fStatKey_Defense\x10\x03\x12\x19\n" +
	"\x15StatKey_SpecialAttack\x10\x04\x12\x1a\n" +
	"\x16StatKey_SpecialDefense\x10\x05\x12\x11\n" +
	"\rStatKey_Speed\x10\x06\x12\x14\n" +
	"\x10StatKey_Accuracy\x10\a\x12\x13\n" +
	"\x0fStatKey_Evasion\x10\bB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Poke_proto_rawDescOnce sync.Once
	file_MainServer_Poke_proto_rawDescData []byte
)

func file_MainServer_Poke_proto_rawDescGZIP() []byte {
	file_MainServer_Poke_proto_rawDescOnce.Do(func() {
		file_MainServer_Poke_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Poke_proto_rawDesc), len(file_MainServer_Poke_proto_rawDesc)))
	})
	return file_MainServer_Poke_proto_rawDescData
}

var file_MainServer_Poke_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_MainServer_Poke_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_MainServer_Poke_proto_goTypes = []any{
	(PokeLiveStatus)(0),                  // 0: MainServer.PokeLiveStatus
	(PokeTypeEnum)(0),                    // 1: MainServer.PokeTypeEnum
	(Nature)(0),                          // 2: MainServer.Nature
	(Gender)(0),                          // 3: MainServer.Gender
	(PokemonStatKey)(0),                  // 4: MainServer.PokemonStatKey
	(*Poke)(nil),                         // 5: MainServer.Poke
	(*PokeStatusInfo)(nil),               // 6: MainServer.PokeStatusInfo
	(*PokeBorrowInfo)(nil),               // 7: MainServer.PokeBorrowInfo
	(*HonorInfo)(nil),                    // 8: MainServer.HonorInfo
	(*SaleInfo)(nil),                     // 9: MainServer.SaleInfo
	(*PokeStat)(nil),                     // 10: MainServer.PokeStat
	(*PokeSimpleMove)(nil),               // 11: MainServer.PokeSimpleMove
	(*BornInfo)(nil),                     // 12: MainServer.BornInfo
	(*PokeSysExtra)(nil),                 // 13: MainServer.PokeSysExtra
	(*PokeExtra)(nil),                    // 14: MainServer.PokeExtra
	(*PokeLastLevelUpInfo)(nil),          // 15: MainServer.PokeLastLevelUpInfo
	(*LevelUpLearnMoveRequest)(nil),      // 16: MainServer.LevelUpLearnMoveRequest
	(*RpcPokeUpdateRequest)(nil),         // 17: MainServer.RpcPokeUpdateRequest
	(*RpcPokeUpdateResponse)(nil),        // 18: MainServer.RpcPokeUpdateResponse
	(*RpcPokeChangeEvsRequest)(nil),      // 19: MainServer.RpcPokeChangeEvsRequest
	(*RpcPokeChangeEvsResponse)(nil),     // 20: MainServer.RpcPokeChangeEvsResponse
	(*RpcPokeChangeAbilityRequest)(nil),  // 21: MainServer.RpcPokeChangeAbilityRequest
	(*RpcPokeChangeAbilityResponse)(nil), // 22: MainServer.RpcPokeChangeAbilityResponse
	(*RpcPokeChangeMoveRequest)(nil),     // 23: MainServer.RpcPokeChangeMoveRequest
	(*RpcPokeChangeMoveResponse)(nil),    // 24: MainServer.RpcPokeChangeMoveResponse
	(*RpcPokeUseCapRequest)(nil),         // 25: MainServer.RpcPokeUseCapRequest
	(*RpcPokeUseCapResponse)(nil),        // 26: MainServer.RpcPokeUseCapResponse
	(*RpcPokeChangeNatureRequest)(nil),   // 27: MainServer.RpcPokeChangeNatureRequest
	(*RpcPokeChangeNatureResponse)(nil),  // 28: MainServer.RpcPokeChangeNatureResponse
}
var file_MainServer_Poke_proto_depIdxs = []int32{
	10, // 0: MainServer.Poke.evs:type_name -> MainServer.PokeStat
	10, // 1: MainServer.Poke.ivs:type_name -> MainServer.PokeStat
	9,  // 2: MainServer.Poke.sale_info:type_name -> MainServer.SaleInfo
	7,  // 3: MainServer.Poke.borrow_info:type_name -> MainServer.PokeBorrowInfo
	11, // 4: MainServer.Poke.moves:type_name -> MainServer.PokeSimpleMove
	2,  // 5: MainServer.Poke.nature:type_name -> MainServer.Nature
	6,  // 6: MainServer.Poke.status:type_name -> MainServer.PokeStatusInfo
	12, // 7: MainServer.Poke.born:type_name -> MainServer.BornInfo
	3,  // 8: MainServer.Poke.gender:type_name -> MainServer.Gender
	13, // 9: MainServer.Poke.sys_extra:type_name -> MainServer.PokeSysExtra
	14, // 10: MainServer.Poke.extra:type_name -> MainServer.PokeExtra
	8,  // 11: MainServer.Poke.honorInfo:type_name -> MainServer.HonorInfo
	0,  // 12: MainServer.PokeStatusInfo.live_status:type_name -> MainServer.PokeLiveStatus
	10, // 13: MainServer.PokeExtra.tempEvs:type_name -> MainServer.PokeStat
	15, // 14: MainServer.PokeExtra.lastLevelUpInfo:type_name -> MainServer.PokeLastLevelUpInfo
	15, // 15: MainServer.LevelUpLearnMoveRequest.lastLevelUpInfo:type_name -> MainServer.PokeLastLevelUpInfo
	11, // 16: MainServer.LevelUpLearnMoveRequest.moves:type_name -> MainServer.PokeSimpleMove
	10, // 17: MainServer.RpcPokeChangeEvsRequest.evs:type_name -> MainServer.PokeStat
	10, // 18: MainServer.RpcPokeChangeEvsResponse.evs:type_name -> MainServer.PokeStat
	11, // 19: MainServer.RpcPokeChangeMoveResponse.moves:type_name -> MainServer.PokeSimpleMove
	4,  // 20: MainServer.RpcPokeUseCapRequest.stat_key:type_name -> MainServer.PokemonStatKey
	4,  // 21: MainServer.RpcPokeUseCapResponse.stat_key:type_name -> MainServer.PokemonStatKey
	2,  // 22: MainServer.RpcPokeChangeNatureRequest.nature:type_name -> MainServer.Nature
	2,  // 23: MainServer.RpcPokeChangeNatureResponse.nature:type_name -> MainServer.Nature
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_MainServer_Poke_proto_init() }
func file_MainServer_Poke_proto_init() {
	if File_MainServer_Poke_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Poke_proto_rawDesc), len(file_MainServer_Poke_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Poke_proto_goTypes,
		DependencyIndexes: file_MainServer_Poke_proto_depIdxs,
		EnumInfos:         file_MainServer_Poke_proto_enumTypes,
		MessageInfos:      file_MainServer_Poke_proto_msgTypes,
	}.Build()
	File_MainServer_Poke_proto = out.File
	file_MainServer_Poke_proto_goTypes = nil
	file_MainServer_Poke_proto_depIdxs = nil
}
