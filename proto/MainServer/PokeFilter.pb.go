// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeFilter.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeFilterSort int32

const (
	PokeFilterSort_NONE           PokeFilterSort = 0 // 无排序
	PokeFilterSort_PRICE_ASC      PokeFilterSort = 1 // 价格升序
	PokeFilterSort_PRICE_DESC     PokeFilterSort = 2 // 价格降序
	PokeFilterSort_LEVEL_ASC      PokeFilterSort = 3 // 等级升序
	PokeFilterSort_LEVEL_DESC     PokeFilterSort = 4 // 等级降序
	PokeFilterSort_UPDATE_TS_ASC  PokeFilterSort = 5 // 更新时间升序
	PokeFilterSort_UPDATE_TS_DESC PokeFilterSort = 6 // 更新时间降序
)

// Enum value maps for PokeFilterSort.
var (
	PokeFilterSort_name = map[int32]string{
		0: "NONE",
		1: "PRICE_ASC",
		2: "PRICE_DESC",
		3: "LEVEL_ASC",
		4: "LEVEL_DESC",
		5: "UPDATE_TS_ASC",
		6: "UPDATE_TS_DESC",
	}
	PokeFilterSort_value = map[string]int32{
		"NONE":           0,
		"PRICE_ASC":      1,
		"PRICE_DESC":     2,
		"LEVEL_ASC":      3,
		"LEVEL_DESC":     4,
		"UPDATE_TS_ASC":  5,
		"UPDATE_TS_DESC": 6,
	}
)

func (x PokeFilterSort) Enum() *PokeFilterSort {
	p := new(PokeFilterSort)
	*p = x
	return p
}

func (x PokeFilterSort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeFilterSort) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeFilter_proto_enumTypes[0].Descriptor()
}

func (PokeFilterSort) Type() protoreflect.EnumType {
	return &file_MainServer_PokeFilter_proto_enumTypes[0]
}

func (x PokeFilterSort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeFilterSort.Descriptor instead.
func (PokeFilterSort) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeFilter_proto_rawDescGZIP(), []int{0}
}

//	enum PokeFilterSort {
//	    ivs = 0; //默认用成长值总和排序
//	    evs = 1;
//	    price = 2;
//	}
//
// 在前端进行名称筛选 再传到后面
type PokeFilter struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MinEvs         *PokeStat              `protobuf:"bytes,1,opt,name=min_evs,json=minEvs,proto3" json:"min_evs,omitempty"`
	MaxEvs         *PokeStat              `protobuf:"bytes,2,opt,name=max_evs,json=maxEvs,proto3" json:"max_evs,omitempty"`
	MinIvs         *PokeStat              `protobuf:"bytes,3,opt,name=min_ivs,json=minIvs,proto3" json:"min_ivs,omitempty"`
	MaxIvs         *PokeStat              `protobuf:"bytes,4,opt,name=max_ivs,json=maxIvs,proto3" json:"max_ivs,omitempty"`
	Names          []string               `protobuf:"bytes,5,rep,name=names,proto3" json:"names,omitempty"`
	Sort           PokeFilterSort         `protobuf:"varint,6,opt,name=sort,proto3,enum=MainServer.PokeFilterSort" json:"sort,omitempty"`
	MinPrice       int32                  `protobuf:"varint,7,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice       int32                  `protobuf:"varint,8,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	MinSpecialCoin int32                  `protobuf:"varint,9,opt,name=min_special_coin,json=minSpecialCoin,proto3" json:"min_special_coin,omitempty"`
	MaxSpecialCoin int32                  `protobuf:"varint,10,opt,name=max_special_coin,json=maxSpecialCoin,proto3" json:"max_special_coin,omitempty"`
	UpdateTs       int64                  `protobuf:"varint,11,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Sale           bool                   `protobuf:"varint,12,opt,name=sale,proto3" json:"sale,omitempty"`
	Owner          bool                   `protobuf:"varint,13,opt,name=owner,proto3" json:"owner,omitempty"`
	Gender         string                 `protobuf:"bytes,14,opt,name=gender,proto3" json:"gender,omitempty"`
	Ability        string                 `protobuf:"bytes,15,opt,name=ability,proto3" json:"ability,omitempty"`
	Page           int32                  `protobuf:"varint,16,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32                  `protobuf:"varint,17,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PokeFilter) Reset() {
	*x = PokeFilter{}
	mi := &file_MainServer_PokeFilter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeFilter) ProtoMessage() {}

func (x *PokeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeFilter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeFilter.ProtoReflect.Descriptor instead.
func (*PokeFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeFilter_proto_rawDescGZIP(), []int{0}
}

func (x *PokeFilter) GetMinEvs() *PokeStat {
	if x != nil {
		return x.MinEvs
	}
	return nil
}

func (x *PokeFilter) GetMaxEvs() *PokeStat {
	if x != nil {
		return x.MaxEvs
	}
	return nil
}

func (x *PokeFilter) GetMinIvs() *PokeStat {
	if x != nil {
		return x.MinIvs
	}
	return nil
}

func (x *PokeFilter) GetMaxIvs() *PokeStat {
	if x != nil {
		return x.MaxIvs
	}
	return nil
}

func (x *PokeFilter) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *PokeFilter) GetSort() PokeFilterSort {
	if x != nil {
		return x.Sort
	}
	return PokeFilterSort_NONE
}

func (x *PokeFilter) GetMinPrice() int32 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

func (x *PokeFilter) GetMaxPrice() int32 {
	if x != nil {
		return x.MaxPrice
	}
	return 0
}

func (x *PokeFilter) GetMinSpecialCoin() int32 {
	if x != nil {
		return x.MinSpecialCoin
	}
	return 0
}

func (x *PokeFilter) GetMaxSpecialCoin() int32 {
	if x != nil {
		return x.MaxSpecialCoin
	}
	return 0
}

func (x *PokeFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PokeFilter) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *PokeFilter) GetOwner() bool {
	if x != nil {
		return x.Owner
	}
	return false
}

func (x *PokeFilter) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *PokeFilter) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *PokeFilter) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PokeFilter) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type RpcPokeInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeInfoRequest) Reset() {
	*x = RpcPokeInfoRequest{}
	mi := &file_MainServer_PokeFilter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeInfoRequest) ProtoMessage() {}

func (x *RpcPokeInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeFilter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeInfoRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeInfoRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeFilter_proto_rawDescGZIP(), []int{1}
}

func (x *RpcPokeInfoRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

var File_MainServer_PokeFilter_proto protoreflect.FileDescriptor

const file_MainServer_PokeFilter_proto_rawDesc = "" +
	"\n" +
	"\x1bMainServer/PokeFilter.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\"\xc6\x04\n" +
	"\n" +
	"PokeFilter\x12-\n" +
	"\amin_evs\x18\x01 \x01(\v2\x14.MainServer.PokeStatR\x06minEvs\x12-\n" +
	"\amax_evs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\x06maxEvs\x12-\n" +
	"\amin_ivs\x18\x03 \x01(\v2\x14.MainServer.PokeStatR\x06minIvs\x12-\n" +
	"\amax_ivs\x18\x04 \x01(\v2\x14.MainServer.PokeStatR\x06maxIvs\x12\x14\n" +
	"\x05names\x18\x05 \x03(\tR\x05names\x12.\n" +
	"\x04sort\x18\x06 \x01(\x0e2\x1a.MainServer.PokeFilterSortR\x04sort\x12\x1b\n" +
	"\tmin_price\x18\a \x01(\x05R\bminPrice\x12\x1b\n" +
	"\tmax_price\x18\b \x01(\x05R\bmaxPrice\x12(\n" +
	"\x10min_special_coin\x18\t \x01(\x05R\x0eminSpecialCoin\x12(\n" +
	"\x10max_special_coin\x18\n" +
	" \x01(\x05R\x0emaxSpecialCoin\x12\x1b\n" +
	"\tupdate_ts\x18\v \x01(\x03R\bupdateTs\x12\x12\n" +
	"\x04sale\x18\f \x01(\bR\x04sale\x12\x14\n" +
	"\x05owner\x18\r \x01(\bR\x05owner\x12\x16\n" +
	"\x06gender\x18\x0e \x01(\tR\x06gender\x12\x18\n" +
	"\aability\x18\x0f \x01(\tR\aability\x12\x12\n" +
	"\x04page\x18\x10 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x11 \x01(\x05R\bpageSize\"-\n" +
	"\x12RpcPokeInfoRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId*\x7f\n" +
	"\x0ePokeFilterSort\x12\b\n" +
	"\x04NONE\x10\x00\x12\r\n" +
	"\tPRICE_ASC\x10\x01\x12\x0e\n" +
	"\n" +
	"PRICE_DESC\x10\x02\x12\r\n" +
	"\tLEVEL_ASC\x10\x03\x12\x0e\n" +
	"\n" +
	"LEVEL_DESC\x10\x04\x12\x11\n" +
	"\rUPDATE_TS_ASC\x10\x05\x12\x12\n" +
	"\x0eUPDATE_TS_DESC\x10\x06B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeFilter_proto_rawDescOnce sync.Once
	file_MainServer_PokeFilter_proto_rawDescData []byte
)

func file_MainServer_PokeFilter_proto_rawDescGZIP() []byte {
	file_MainServer_PokeFilter_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeFilter_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeFilter_proto_rawDesc), len(file_MainServer_PokeFilter_proto_rawDesc)))
	})
	return file_MainServer_PokeFilter_proto_rawDescData
}

var file_MainServer_PokeFilter_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_PokeFilter_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_PokeFilter_proto_goTypes = []any{
	(PokeFilterSort)(0),        // 0: MainServer.PokeFilterSort
	(*PokeFilter)(nil),         // 1: MainServer.PokeFilter
	(*RpcPokeInfoRequest)(nil), // 2: MainServer.RpcPokeInfoRequest
	(*PokeStat)(nil),           // 3: MainServer.PokeStat
}
var file_MainServer_PokeFilter_proto_depIdxs = []int32{
	3, // 0: MainServer.PokeFilter.min_evs:type_name -> MainServer.PokeStat
	3, // 1: MainServer.PokeFilter.max_evs:type_name -> MainServer.PokeStat
	3, // 2: MainServer.PokeFilter.min_ivs:type_name -> MainServer.PokeStat
	3, // 3: MainServer.PokeFilter.max_ivs:type_name -> MainServer.PokeStat
	0, // 4: MainServer.PokeFilter.sort:type_name -> MainServer.PokeFilterSort
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_MainServer_PokeFilter_proto_init() }
func file_MainServer_PokeFilter_proto_init() {
	if File_MainServer_PokeFilter_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeFilter_proto_rawDesc), len(file_MainServer_PokeFilter_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeFilter_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeFilter_proto_depIdxs,
		EnumInfos:         file_MainServer_PokeFilter_proto_enumTypes,
		MessageInfos:      file_MainServer_PokeFilter_proto_msgTypes,
	}.Build()
	File_MainServer_PokeFilter_proto = out.File
	file_MainServer_PokeFilter_proto_goTypes = nil
	file_MainServer_PokeFilter_proto_depIdxs = nil
}
